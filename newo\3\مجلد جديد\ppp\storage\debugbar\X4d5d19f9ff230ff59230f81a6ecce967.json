{"__meta": {"id": "X4d5d19f9ff230ff59230f81a6ecce967", "datetime": "2025-06-21 01:34:22", "utime": **********.085606, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750469660.674055, "end": **********.085631, "duration": 1.4115757942199707, "duration_str": "1.41s", "measures": [{"label": "Booting", "start": 1750469660.674055, "relative_start": 0, "end": 1750469661.916395, "relative_end": 1750469661.916395, "duration": 1.242339849472046, "duration_str": "1.24s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1750469661.91642, "relative_start": 1.2423648834228516, "end": **********.085635, "relative_end": 4.0531158447265625e-06, "duration": 0.16921496391296387, "duration_str": "169ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46457656, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.019379999999999998, "accumulated_duration_str": "19.38ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0177848, "duration": 0.01622, "duration_str": "16.22ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 83.695}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.0594091, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 83.695, "width_percent": 8.669}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0675168, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "ty", "start_percent": 92.363, "width_percent": 7.637}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "bSqGKRzn8zrN52whBJ5jpetFrYHJ06phOnPEUsYY", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-368780848 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-368780848\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-890860576 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-890860576\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-619201016 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bSqGKRzn8zrN52whBJ5jpetFrYHJ06phOnPEUsYY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-619201016\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-656587413 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1833 characters\">_clck=h9hzj7%7C2%7Cfwy%7C0%7C1998; _clsk=1pfi5p6%7C1750469631526%7C1%7C1%7Cj.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImNLb1F5bUwrcnFtSmo1REozUW9Rc1E9PSIsInZhbHVlIjoiR21laVBNektOSzVDSy8xM1M4bEdtaXEwUEczYUd2MHVTcXI0dTJaQXZVT2doYW9XbWxoYmJUc3dxQXJIZXRMekg1STdvcWxodjJHeU0zUWVhN2wyUGNHcTNFWXdpYzZVUVBYWHlKRjkrRHlvUyswUHFEL1FIaTQxZTdCeWJxUExIUytpTHJ2ZFpYK0ZwcjUycDVXTUNVS1ZpaGc2eC9LTmJ0ZTQ1VS9xTWp6alVUU1h3c1BQVDRqUFZMSk9oaE5CZkFEK0EyR2NGOEJXMTMzMTRzODF5UElvY0d5QXF4WHB6aTRiTmdGRUFpRWhUbEhYUVpPSGxDMDlod0VtRDBadTlqbXkzR1plUnF5K3JwaWxBMllLZ1A0UDQ4OFpXNzdOa2E5Y2FMZzFoKy9DQ0xaaTZyWWJDeVF0Y1daN3A0a3FTQVlpTzN6NS9ZT2FhaWs5NzVaeThpSk1MZVFENjBDMFRHMk9XUXJJOTdHS1RVREZpZHpoU1M3UzY5cnZENzd3Z3pyR2JvU25QZDgzNVUrQWgwTDUxcGp3Y3U4TDNmbnFiV1hIMmF3UFkyYithbldqWGpqbDhtZzdqZ0ZuaE80eTFQWE1YOXc0NWJScXlvRm5KeHE2VDkxVndpVGMydEJxZlVObHVOdWdzVWZGTG1sVmZ5MVpIaHhSKzlFVWVKVDYiLCJtYWMiOiJhZGZmNDkyN2UxOGU5NzkwNzUwMjMzYjAwNDQ5ZjhkMzBlMzRjODAwYjc2OTA2N2Y1OTU3Yjc4MDBiYjliNzlmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Iks5MWFWRi9yZlFvUEh3WXBYNzdnUGc9PSIsInZhbHVlIjoiOU0wVWN4M3llMVN0WVNRVThUWlVHSGpZN0ZoblNSZ3FvNFppRjU4TjhqNWhESjJRRFhyVDNqbzBBMGM3cVM2b2ZJSDNWZXZ1Ly96NDlSaVZ4V3ZmRFBKc01obWR6dm1GZWRseDkyYjRVWnpiODZRR2o0aERVOFZxQ2hVV0RleDFEcVUvWnp6c2xwVXB1OFhuVlJraURoeTFJQkRHOVdoV3draEpFOFdRRVNoRlQwMU0wMW5GM0hXbjYzTUpvbDl1L2lONUZMZUt3eUxXQlViRGEvMTRoQzlUcGpoZ1kwRkNQMEpSeHdHVXJLaVRRVmg4S1lEbG90VHF6SmRXR0c2VlpwTXQ4bVVaVjdLS3Fqd09QczU2TDc0QncyTTkvL2R6VVBqY2lEN1k2cGVIQlhxQnZ3OU5BendqZE9RWUttUmhxdkFQSEpFd2FyNHhIdlUrVGlxRmpWYUJwRXNGN0FxcFl0eDdjY2JqRThmNUsxZ1VFLzJud0xvQzRXcjlCRkJ6OExhaTIvUU15YW9iZy96eG9adGJZNnNjbi9RVUdRbGd0ZU9zcm9SdXlnNVhCa0w2Mk1GUEo0UU4rbkxTUE8yQ1dLUGhYTWdVdWpiLzg2RmNrb21WaldGYWUwOERjNWNvK095WFhwRDAyTUxpZHRnT2dyZnRySExCWWFpc2pmSGQiLCJtYWMiOiI0YWI1YmNhZDYwNTA2OGEyMDI3ZGVlYmFlZmZjOTA0NjY5YmIwZjk3ODMyN2NkMjM3OWI5ODM4NTBkN2I5ZmUxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-656587413\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bSqGKRzn8zrN52whBJ5jpetFrYHJ06phOnPEUsYY</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HB3SpfsNW9qh15emZQ8PQBnZsYUESoWIqimBy8Yg</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1888604213 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 21 Jun 2025 01:34:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjZscVlqYndiMm5Rb3lsMTh6TDA5TEE9PSIsInZhbHVlIjoibklkUC9uc2xxSEM0TUl0OTZ4S3llTGlvSkhxTlBOZFhzZlZyODFXaXBmMkc5REJCR0dGK25GM3dJeFNZOTV2WEhtM20xcjdQN3VqQW9CZXVFcWgxbXV6ZGRLMlYrSE16WExWb29IZHZ6NGdUeWp4WXVvSmZqWnBJRkYvdVNtT2hPNWV0VGs0KzNiVjNqd2syRkZxYUxBVExEbEFQa09hWUR0NTFweUgzWjNyeFF3TS91ckIzM1cxem9LYzY3R2U0TWhkL3ZHOERxYWtoT1k3Y01PTFBwYU9LcG14SHdSNVNWMnlPZVpzRDJFSEMyYXVtc2ExTytlY3ZrQmJNbFlBQzQxRHdic2FjcnhhZDYwRk1RWjQrVkhlQytlbVRNakMrTmFRTDB3NElsRG9rNml2cE1na3NTa0FkS0ZKNHB3QkVibzVpSmNHTjV6RzJHWmFhdHZReDFQc3crWWNocTJ3NUUwakVFOEJKeWNNV09IVGxkSHhhVHpNcHIvSVdSYTdBOHkwMC9USS9iTEFBNDRtZ1FIQVZNL1ZmSDRkUDdsRGkyUklyY1pubk1hWVZad0pxdk1hTUpTT2tKamQ0SHp2TElEc083RTVlcTRxQlZzNDArMDdKMDZKT09rSGtUbTU3b3RWNXFibUlYaUVMempFSkNBeXlJQUh3aW5MS0ZKM20iLCJtYWMiOiJkOTlkNTM3M2ZlMzQ1ZjFjNjk5NzNmMjE4Nzk0MzFiMDZhMTkxMzYyYjdjMDAzMjBjNGVkMzczZDE3MmU0MzQ3IiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 03:34:22 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Imp4YW1LNmJaZVJqYmpZZ1R6K2paNnc9PSIsInZhbHVlIjoiUlJqOXQxSUdNN1BlTjREd2NVMUVtM3pDN0VTbFBXVmYySVZqNTJUQVhrOFo0YkN3QjBLZmRlNEpTRlU4SXpsemJRQnNpWURNcHlKTW9ldTFwMWR0Z2tXZE16TkdXbDFzV0UyUGZGQlEwa0liRTBXdUZpNHpNcGRVdjV0WjRDL3FSb0pJZTBpRzUrTHRPZS8vRTBEdDE1Mm4zbmJHTGRXSlBja0puSEhWamFDVDFiamFMN1NjaWdjWG5uS2xpYWlLQ3FYMmxOM3pOYk8rUFN1UStKbXJjeWRqcENzaUwxSkNFM2wzelRxM0wrcDVtMWhVdjZZdzhEc0xBanVuWE1XcEFKWTJPQ0VZelMzSFBsYWZYWE9xR21QcHJiVENnMGNMbkdYall2WjFLTUNJSHdEa3VOdzNON3hnVHk3N24reDFuWDZra2VHdFgyd0JrZU1Bb29ueFdmWkVXTmhMNGFCWkU4SUU5akxhWTIxbFoxalpsSkRCN2podm5CSXBFODlTMzBvZ2pnV1lsSmJUUnplOTl6aWx3WnA5emZoRmwvQUNoeXNrRjlWM1pEQnAydWU0U2crc3ErU0wrcWIwUGVBcnU1Q0tENVh0UWVPbUU3SFhIK1haV3BVN0Q2amdleWR5bUxsd0xRbWN5cUJIVzRHWkVkNzlzVWRVNEc3RVFnYU8iLCJtYWMiOiIxZmNiNzVmMzBiM2ZmNGMxZGFiMTg4NjYzOGEyZGFjZjQ1ZjRiMjhhNzRiYjU3MzMzM2E0Y2RiN2I3M2ViZjA1IiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 03:34:22 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjZscVlqYndiMm5Rb3lsMTh6TDA5TEE9PSIsInZhbHVlIjoibklkUC9uc2xxSEM0TUl0OTZ4S3llTGlvSkhxTlBOZFhzZlZyODFXaXBmMkc5REJCR0dGK25GM3dJeFNZOTV2WEhtM20xcjdQN3VqQW9CZXVFcWgxbXV6ZGRLMlYrSE16WExWb29IZHZ6NGdUeWp4WXVvSmZqWnBJRkYvdVNtT2hPNWV0VGs0KzNiVjNqd2syRkZxYUxBVExEbEFQa09hWUR0NTFweUgzWjNyeFF3TS91ckIzM1cxem9LYzY3R2U0TWhkL3ZHOERxYWtoT1k3Y01PTFBwYU9LcG14SHdSNVNWMnlPZVpzRDJFSEMyYXVtc2ExTytlY3ZrQmJNbFlBQzQxRHdic2FjcnhhZDYwRk1RWjQrVkhlQytlbVRNakMrTmFRTDB3NElsRG9rNml2cE1na3NTa0FkS0ZKNHB3QkVibzVpSmNHTjV6RzJHWmFhdHZReDFQc3crWWNocTJ3NUUwakVFOEJKeWNNV09IVGxkSHhhVHpNcHIvSVdSYTdBOHkwMC9USS9iTEFBNDRtZ1FIQVZNL1ZmSDRkUDdsRGkyUklyY1pubk1hWVZad0pxdk1hTUpTT2tKamQ0SHp2TElEc083RTVlcTRxQlZzNDArMDdKMDZKT09rSGtUbTU3b3RWNXFibUlYaUVMempFSkNBeXlJQUh3aW5MS0ZKM20iLCJtYWMiOiJkOTlkNTM3M2ZlMzQ1ZjFjNjk5NzNmMjE4Nzk0MzFiMDZhMTkxMzYyYjdjMDAzMjBjNGVkMzczZDE3MmU0MzQ3IiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 03:34:22 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Imp4YW1LNmJaZVJqYmpZZ1R6K2paNnc9PSIsInZhbHVlIjoiUlJqOXQxSUdNN1BlTjREd2NVMUVtM3pDN0VTbFBXVmYySVZqNTJUQVhrOFo0YkN3QjBLZmRlNEpTRlU4SXpsemJRQnNpWURNcHlKTW9ldTFwMWR0Z2tXZE16TkdXbDFzV0UyUGZGQlEwa0liRTBXdUZpNHpNcGRVdjV0WjRDL3FSb0pJZTBpRzUrTHRPZS8vRTBEdDE1Mm4zbmJHTGRXSlBja0puSEhWamFDVDFiamFMN1NjaWdjWG5uS2xpYWlLQ3FYMmxOM3pOYk8rUFN1UStKbXJjeWRqcENzaUwxSkNFM2wzelRxM0wrcDVtMWhVdjZZdzhEc0xBanVuWE1XcEFKWTJPQ0VZelMzSFBsYWZYWE9xR21QcHJiVENnMGNMbkdYall2WjFLTUNJSHdEa3VOdzNON3hnVHk3N24reDFuWDZra2VHdFgyd0JrZU1Bb29ueFdmWkVXTmhMNGFCWkU4SUU5akxhWTIxbFoxalpsSkRCN2podm5CSXBFODlTMzBvZ2pnV1lsSmJUUnplOTl6aWx3WnA5emZoRmwvQUNoeXNrRjlWM1pEQnAydWU0U2crc3ErU0wrcWIwUGVBcnU1Q0tENVh0UWVPbUU3SFhIK1haV3BVN0Q2amdleWR5bUxsd0xRbWN5cUJIVzRHWkVkNzlzVWRVNEc3RVFnYU8iLCJtYWMiOiIxZmNiNzVmMzBiM2ZmNGMxZGFiMTg4NjYzOGEyZGFjZjQ1ZjRiMjhhNzRiYjU3MzMzM2E0Y2RiN2I3M2ViZjA1IiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 03:34:22 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1888604213\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bSqGKRzn8zrN52whBJ5jpetFrYHJ06phOnPEUsYY</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}