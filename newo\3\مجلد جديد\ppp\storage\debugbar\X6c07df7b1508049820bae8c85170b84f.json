{"__meta": {"id": "X6c07df7b1508049820bae8c85170b84f", "datetime": "2025-06-21 01:35:27", "utime": **********.430971, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.187004, "end": **********.431005, "duration": 1.****************, "duration_str": "1.24s", "measures": [{"label": "Booting", "start": **********.187004, "relative_start": 0, "end": **********.251904, "relative_end": **********.251904, "duration": 1.****************, "duration_str": "1.06s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.251928, "relative_start": 1.****************, "end": **********.431009, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "179ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.024249999999999997, "accumulated_duration_str": "24.25ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.338097, "duration": 0.02122, "duration_str": "21.22ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 87.505}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.383882, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 87.505, "width_percent": 5.031}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.4104729, "duration": 0.00181, "duration_str": "1.81ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 92.536, "width_percent": 7.464}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "OxCA9rABwnsUtdJrsRyx2DLpR7CHtGXPC1CLyExX", "_previous": "array:1 [\n  \"url\" => \"http://localhost/forms\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/forms</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwy%7C0%7C1960; _clsk=1updagk%7C1750469725331%7C6%7C1%7Cj.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InQ2ZnNKdStJeFFHTjhUUTdpUDlFWmc9PSIsInZhbHVlIjoiejJDM1VSSU9LbnhNeWt6SjJkSXovaHRSdHNhVUdKVkI0WWxqTjQvUTVmRmU3UG1mY2FQUVpSUXpPWHhKd1BuV20vVnBnbisvblEwRXN2OSt6TlBkU1BGYkJ1Z1ZYWU9CQU9WaGoreGpOV0JGK2lIOWg5S3BzdkJ6QzBuYlljWE1XS0w5MzQ3V3JzV0ZJMWw5TURZaTA3VU94QVBwc21iYjNOUzdXVTV5Y0hUR0dIb3JPNnJDaGM4MGZlaDlPMG0rdHp0NFhBMEZwYzN1VnJDSXFTWURGSXY0dzdkM2RxNHdsQmRiT3k2WG43RXpTTjg3ZERNOWlqSkxCOTY3MklNd1R4bUE0SVgwdXVxeUtLVEhzRHpXRVBDMXhRZGRaa3hFYzdSMWpxdjFOTGFrZlZha2lENEgzTXJWWU42OEJUdThRenRVQm9rSzhaT3NMcmNoNGlKOVMyeXFkZkF5c0NTTDV4NWNVaFBENjdySjlGOWlIMjIxeEd3M00wUGN6ejQ0SU13ZFpxUzV6UnJnTXY2bnNaQXpNMDVXSEkxVGY5d2ZTNUhMOTduRjNhZVYyYm5lTlBPK3ptY2owaXVtWlN6RmJwNlBiZll4cDVDRmJRVkRoamI4ZFA2ZDFidnZsdkMrQVdzSDUyZkQ1QkhvNWVKd20yeXR6YnpWWjFnOUcwNGYiLCJtYWMiOiI3NWJhYjM5ZGYzMjA4MmQ2MDliNTdjMjIzOTI0OGEyZjBjZWM2NDQ0MDIxOWZkODJiMDRkMzEzNzczYzEyMDM2IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkZiREJQRTYwaDQxQ2FIeVFteStTR0E9PSIsInZhbHVlIjoiRk5jS01LOWFKbkpJaEpTaVBLdG1ZZ2R4alB3SDhiVjN4Z0taWlU0VFJPbnJwTVlpeHpQbUhHT3YxejZTNjdaUkpyUlJGOHIzRHBpSUN2OHhUV0JuWkVsTkgrL1Y5MXE4b09rNGFsVkdlT0pKLytncnl4UlJrVW01WHQxNnFKeVg5YmR2TlJsUUNRRDNTeURzM0l6cjZ5WWdoQUJhOXFkOWc4MTRVTjM5eXpYMXVIZEFsRW9BOHc4TFVCbENUYnJsa243Qk1ieWhDVXJKU3pLT1R0NGwrZ3g2TEJrZHV5WXVzV3hndUowRTlBT1VlL240RFhYbHpXbUFlbXl3WGVxbU96TSs1cnFnbjlGVC8wbmRWT0t1akdKZEJCU01sSFdjSmxKQXBpVzVqMVdlWHYzZ05DajNSMUFPMW1ybnZWTjdEZWFZbmkwODA1WWpXdEtRUHZtcUl2OGVsTHJTdEE5RkpaYzZ2bm9FaG1BRVpxWjdxazVXeDg5ZHpnZjNBczFyaURZeVFtVlNpRjZzWHpVTU55dEpBSTkzbmNXTkhKdGhxQlJWQXhBU1BsNENvODdlZklpN25CY2lPdDZ6QzByQ1RSdXl0SW1IRVlGL2tSNDNYaWVaMEtMdUsySXBkaVVySERjQmdXSVR3cHVDd2RLLzQ5cjM3RytITVVUNWFNSE8iLCJtYWMiOiI4NTBhMmYxYWZmZDY0ZTllODQyZjYxM2U2MmUyMGM1NzYwODFhODdhNDVkM2FmNTBlYjY4NzNmOTRjYTYzZTRkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1555376937 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OxCA9rABwnsUtdJrsRyx2DLpR7CHtGXPC1CLyExX</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">h4v7KnclY0iOKyPMFu5GxMLqVTN5plmJ6SYNK0tY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1555376937\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-726862169 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 21 Jun 2025 01:35:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application, X-CSRF-TOKEN</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Inljb0RMOHdLS0h6SzlYVVRVMTNBQkE9PSIsInZhbHVlIjoibjVJZHJaQXVxNlo2WElpZ013NkhoQUtQc0pua0tJaDVvYWZ0cHNocUxQajFWTStQYmhJWHk1YktES0dzQk4vcVlyTVNxemJEUHhxVldXMThManNZNlFDenZMandjT2ZJeElrN1d6MTJHZmgxZU1GaWVsTytIazhNZ0hjeExsQVpQc3hvcHNDanpJWnZ3czF3K3RvSndlc3QyNi9Hd2lrbUNDeWhlRnNEVCtVR2dXN2syMklRSTRueXJQSnBvcVhtbFlZT1Z0U1BhV2hpeWl4VFg1M0x3a2hTT2praEU1YmJuMnFnOXFDbkRjTnQ5bmQvMFBMTE12M3M2aHluVXZYQTNRWjNaOHpUMTNXc25LdkhrRG9sZmt3VVQ3aFFCdmtQSXREV2M0Y3ZpSzVGRUh6SjNNWUI4Z1RxaUkwWHI5elUzRmN5R2lyZUxsRVFoRGNIQmg2MGV5OGI0NHJSem04ZVhkcTNhRzlYUWV2Nk0zcVFueEFQelNwTzRBY2wzakJaTjEzRTRFR0tFS3VyUGxLNVh0S3c4aUpVL2NnOVNEM1NTYjFKZFp4UXpxcERaMFBlTzBDSEROYWFqRndJbTRnblc3YmhTajlKSVRsQ1ZPaTEwSWplYzAzQ1JTc3hXRHFORzJad1NTVXV4YnRpaXhyTnlnVGNucUdTZm10ancxTjEiLCJtYWMiOiI2MzkwODgwNDVkOWUyZGJlMTRlZTRlNWRjNzZlZmU5NWQxMjdlYTFkYjJkNDNhMmZlZWIxMjZjN2MxMThhMTJkIiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 03:35:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlE2cFFLeXZ4WFBTY0ZwKzBZZEp5YkE9PSIsInZhbHVlIjoiVnpXTDhCMXYxNkNCMVpZWXdSRDVSZWJoWjl5czMyelBWWjNCUU5mOGFtS0p3eGNKNk10Q2N2dmowQlhnTy8wK0psTXpuUDZiMGFwa2pUTnlRT3pSTjdzRE54N0dkRjRtRWJUeHhjeWNGQmpXL1BmNHZEZHovT25jdTVRS1lrVlgwalVCamhxL0M0akQ0WDFTdURxeFd5Z25yUmhDR1dCTEI3MkkwZGxVN0lia01iOGVjTFJHMEY4MVpNYzhNVzEzdWtiTEkvMHdlTDdmelBXS1VPZldHMjQxblVRVUZJSE02Q21JUjZCUkNZNHRMalVGZm5wVkx6b3A1M2NWcEJ3RG9lejNmMkt5bml1U1E3ekNvbENKTHBSdEc2QjlRVFc5YUNZdmNHclBGUjlReE9pa2VncHZyLzJqNG9EQTBPdHg3dnpHTUlLZE1STG5UMWZCQThvSGtyWDVIRlE4NldGZnZ1VE1GVlpmbU5sYnZnaHlTU3ZvY1JsbHo4dFNpQVM1c2RjVU5jRjJSTzlIaTd1MjBua3U4cHAzdFN0WEVYL1FKSFRDc1ZWN0tCL1pIc1FxZVd6L29IVjBObWdUd0pMcG1iVU1aVGkwQXptRGZtYWtyYzhsWVFJL3dWYjZZSStqa3ppbVA3VmR5SlpSRVdhbGVmdjliVWwrVm1NeW90RE0iLCJtYWMiOiIxMDk0Mzg2MDE4ODAzYjI1MmIzZDFkNjViMmM5MzZmM2QwOGY2ZTExZjIzZmQ4NWFkZmYwNDY5NWY4MmIxYzhhIiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 03:35:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Inljb0RMOHdLS0h6SzlYVVRVMTNBQkE9PSIsInZhbHVlIjoibjVJZHJaQXVxNlo2WElpZ013NkhoQUtQc0pua0tJaDVvYWZ0cHNocUxQajFWTStQYmhJWHk1YktES0dzQk4vcVlyTVNxemJEUHhxVldXMThManNZNlFDenZMandjT2ZJeElrN1d6MTJHZmgxZU1GaWVsTytIazhNZ0hjeExsQVpQc3hvcHNDanpJWnZ3czF3K3RvSndlc3QyNi9Hd2lrbUNDeWhlRnNEVCtVR2dXN2syMklRSTRueXJQSnBvcVhtbFlZT1Z0U1BhV2hpeWl4VFg1M0x3a2hTT2praEU1YmJuMnFnOXFDbkRjTnQ5bmQvMFBMTE12M3M2aHluVXZYQTNRWjNaOHpUMTNXc25LdkhrRG9sZmt3VVQ3aFFCdmtQSXREV2M0Y3ZpSzVGRUh6SjNNWUI4Z1RxaUkwWHI5elUzRmN5R2lyZUxsRVFoRGNIQmg2MGV5OGI0NHJSem04ZVhkcTNhRzlYUWV2Nk0zcVFueEFQelNwTzRBY2wzakJaTjEzRTRFR0tFS3VyUGxLNVh0S3c4aUpVL2NnOVNEM1NTYjFKZFp4UXpxcERaMFBlTzBDSEROYWFqRndJbTRnblc3YmhTajlKSVRsQ1ZPaTEwSWplYzAzQ1JTc3hXRHFORzJad1NTVXV4YnRpaXhyTnlnVGNucUdTZm10ancxTjEiLCJtYWMiOiI2MzkwODgwNDVkOWUyZGJlMTRlZTRlNWRjNzZlZmU5NWQxMjdlYTFkYjJkNDNhMmZlZWIxMjZjN2MxMThhMTJkIiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 03:35:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlE2cFFLeXZ4WFBTY0ZwKzBZZEp5YkE9PSIsInZhbHVlIjoiVnpXTDhCMXYxNkNCMVpZWXdSRDVSZWJoWjl5czMyelBWWjNCUU5mOGFtS0p3eGNKNk10Q2N2dmowQlhnTy8wK0psTXpuUDZiMGFwa2pUTnlRT3pSTjdzRE54N0dkRjRtRWJUeHhjeWNGQmpXL1BmNHZEZHovT25jdTVRS1lrVlgwalVCamhxL0M0akQ0WDFTdURxeFd5Z25yUmhDR1dCTEI3MkkwZGxVN0lia01iOGVjTFJHMEY4MVpNYzhNVzEzdWtiTEkvMHdlTDdmelBXS1VPZldHMjQxblVRVUZJSE02Q21JUjZCUkNZNHRMalVGZm5wVkx6b3A1M2NWcEJ3RG9lejNmMkt5bml1U1E3ekNvbENKTHBSdEc2QjlRVFc5YUNZdmNHclBGUjlReE9pa2VncHZyLzJqNG9EQTBPdHg3dnpHTUlLZE1STG5UMWZCQThvSGtyWDVIRlE4NldGZnZ1VE1GVlpmbU5sYnZnaHlTU3ZvY1JsbHo4dFNpQVM1c2RjVU5jRjJSTzlIaTd1MjBua3U4cHAzdFN0WEVYL1FKSFRDc1ZWN0tCL1pIc1FxZVd6L29IVjBObWdUd0pMcG1iVU1aVGkwQXptRGZtYWtyYzhsWVFJL3dWYjZZSStqa3ppbVA3VmR5SlpSRVdhbGVmdjliVWwrVm1NeW90RE0iLCJtYWMiOiIxMDk0Mzg2MDE4ODAzYjI1MmIzZDFkNjViMmM5MzZmM2QwOGY2ZTExZjIzZmQ4NWFkZmYwNDY5NWY4MmIxYzhhIiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 03:35:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-726862169\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-413666867 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OxCA9rABwnsUtdJrsRyx2DLpR7CHtGXPC1CLyExX</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/forms</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-413666867\", {\"maxDepth\":0})</script>\n"}}