{"__meta": {"id": "X3951493454d3eac73bf5bc42b279fa25", "datetime": "2025-06-21 01:35:32", "utime": **********.793267, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750469731.431777, "end": **********.793296, "duration": 1.3615190982818604, "duration_str": "1.36s", "measures": [{"label": "Booting", "start": 1750469731.431777, "relative_start": 0, "end": **********.58956, "relative_end": **********.58956, "duration": 1.157783031463623, "duration_str": "1.16s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.589578, "relative_start": 1.1578009128570557, "end": **********.7933, "relative_end": 3.814697265625e-06, "duration": 0.2037220001220703, "duration_str": "204ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46148200, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00977, "accumulated_duration_str": "9.77ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.679116, "duration": 0.00511, "duration_str": "5.11ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 52.303}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.712004, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 52.303, "width_percent": 12.487}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.75394, "duration": 0.0022, "duration_str": "2.2ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 64.79, "width_percent": 22.518}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.772962, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 87.308, "width_percent": 12.692}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "OxCA9rABwnsUtdJrsRyx2DLpR7CHtGXPC1CLyExX", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-309661105 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-309661105\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1555642479 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1555642479\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1195096217 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OxCA9rABwnsUtdJrsRyx2DLpR7CHtGXPC1CLyExX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1195096217\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1794894546 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwy%7C0%7C1960; _clsk=1updagk%7C1750469725331%7C6%7C1%7Cj.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjhIQWJVV0h2Y2VOdlI4aGZyeGpXdnc9PSIsInZhbHVlIjoiOFN1clNhVkhzV1RTZ3pOWmdVZnR3bmZqLzV3eElycVYwczAxaWRhZjNTYzFTbDIrMnBhVDN4amFSNmpKZlgydDAyUmpUR2RvRDZYSGQ2RXBmWTZSYVNDT01BSG1FMi9QcHU5S1N3TXFROFdtTTNBSjk5V3FRdWtoak5CWnpuRzdzTVpQb0xqWUdaaEUrcnVOcjVxN2E0OHVSQzR6MWxJODFQU3BWekY0SVhraWRQa2pnczJFS0l2WXdEcEJockt2ZFlPWnNmMm5rUFpCYTIrb3FHN0V2WXZ4SFB3VWhYZU9MRnFRczFZSEVMdmJ3VmdQRklnZEZtZXgxa2xHYmE0c1JHOEpqeXRGeWFacTVmVE9BQlBRVld6QUNsQkJWQWlZT1o2WlZPU3BCM0NxNi81dkJ2MGs2R1J6MkI5N3NNN3lyMFdIOXBvODF5VStnbjEwRTdUc1JYVHhITTlTN0FDUlZDZldRUmxOMHpIMG9UVk0zRm1qaUhQMmdVbWs4dFN1R2swSHR3T3BJazNvQ2syODBJaTYrM25VZllmSlVGWkFQT3QyeFFJcUNrZ0xmeDl2YkFvNDdmZVNseFdqUS9QRzcxY1RDTzZDV2NSRnJhOFpEemgyUXRnTTd0WFJFY0FRUG9TSlpTWVNJTUh4RDdOUUV4ZTB5M1o4bVFUdjRlK1kiLCJtYWMiOiIwOWJmMzE2MjhiNTUzMGNjNmQ5MDg1ZDc3MWQ3YTQzN2E5YTkzZWYwNjgyY2M1NjNmNzk5MTRlMmEwYjliOWRiIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImNmUVo1bE1hZTJtQU5rRVdJd3JvclE9PSIsInZhbHVlIjoiQ0I5bE54WUFMSlloRGgydkVPaVQ4QWM2ZWl4VEdYeEZkekRaYU1DUjJ6eFJhM1FWSERYVTdQdVVEOVlPQWRpT3V6RXFZS0ZqTUpzcHlzeHlYK3JzR0JsL2NYbmo1VU9qMVBhSktjL2M1ZWh1NWw1S2pVUGZJTFRvRHFKYjI2a3Z4QU1wQ0VNOGFjWDIyZXdPK01JMEk4L05kZ2FzUXd3ZkZmelAvWXd4N0E5YlFTKzJGT25neHo0TnQ4aFl3MkdaWklMak00ZCtEdEtqeVlMRW91UzArNG9VOWV1bll0UzRGenRyQlNJdGM5a25PUlFkTnRMNVdtdm14TGF2cmJiaVhCV0Q2c3FWZUNhRlM2UEpiNkE4R05DQVprK21iNjVFa1hKeHVpYW1aa2szT2d2WGlWcEtZZjhCSHFYUlZkL0VjUkc1K0VUOEhoUENudGxwT2NaZDZkVTIrbWQyV0pqUlUyQ1Nkb2k5SCtjYjIveXQ0c3FoVXh4bXIzSUdWNGtzc1hzbTByWnBDWEhvMk5ya0VtSXVjWU9pd1BFWDJPcHNZQU5CeGxsdWkzOE5WZVNuZnlhcHpqWHhDR1pmY1AwR1lVVzFabndSbUhadjkvM3hRT1JKc1JpZ3Z5Zi9oVXduWXhSMVFPamd0TWgwcGl2L1dxblRBUXVaMWRZNG5takIiLCJtYWMiOiIwOTViZjk4ZDUwMGM4YmNjYTM2YWIxZjhjOTRlOGJkYjk3NmFiMTJmYmMzMjFmYzg0MTA2OTUzNzg4Mzk3MjczIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1794894546\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-966805250 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OxCA9rABwnsUtdJrsRyx2DLpR7CHtGXPC1CLyExX</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">h4v7KnclY0iOKyPMFu5GxMLqVTN5plmJ6SYNK0tY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-966805250\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-636525064 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 21 Jun 2025 01:35:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlpTeDc4cVdJVmdEODNza2FXcXVRN2c9PSIsInZhbHVlIjoiSy9QYTRrT0svcFpsUTE1SkFGNFhjbEtHS2NWWG5ydWljeUt2T0Rsa2UwZUxVbW9rSHRWOWZLVElqS0ZTSm9ZVVp3a0o2bFlaZU5ISjdPN0x6WnBWRm15ZWF2b2RQZndFdXhBd0h6bE8xQWhEZHpuMWZvL09XbklpWkgvcEtGNFpTUml2aGU3MS9XZnd6cWQrcEpRRzRJOUNhaUx3bnoxWmJEYWIxN0VTQ1lxaWVld2Y2V3dGNlZINzZQaklGY0dOTFhIdUFPdDZ6cEYwd3NGZ21wMkxmY2VKakh3TnJkZXUzWVdzLzNVV2F4QitiejBJR3grdFVFN29MZ2oyRXc4eGgwdklKM0MxeE82U1hMR2cyVVppdzViUklWMTBzZU1aaVRyZU8wQjdZeDE3cU1QNXovdkpRQ3NZN2tXMFFqeXFiTjlCQ1Y2Rk92QmtOdmZBT3ZhcmI2YmlMUmxPN2ltVk9UUC9MMWpheXZ6RmNGSldhOUZOZCs3V294bmxLTUdYSmNaMlFudlJYczRsTkdTdC9tYmxENnJ1Nm1BTEdDOXZROVBxOTRyWExGQzVyV3hmcWd6c3RramtLTncwdFd0c3pSSGF3V3BqemRHN2FtUW9weXA1YURmOWVWb1V5N0s5N29uRXByVER3TmZCUVhRMzVWbWcvRXMwZkR1VENoRWMiLCJtYWMiOiJlZDM4MGYwMTI3MzA0MGZmMjE2ODBjZTEzNGNmNWI3ODc1N2YxNDI2ZTkzNDRlYzIyZGJhMDdjMWZjMjgyNTc5IiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 03:35:32 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlVpOGE0Nks2NkptWkd2MGlOaU1kRWc9PSIsInZhbHVlIjoiaE1rNXd2ZGpCT3BSeTJxSEZGZS9CczlnWTNZK1Rhcmk0V2RNTDcyMXlvUndseXBlU1d0cTJaUnBTWEtyS0JoQjcyaDc3SzhobEU2Zm5oL3pxYmQzK1FnaXJpLzBJWXh0VWlJV1d0Z1dQZHM2cWdsVzFrbkZ6Z3ZkSGNOTFl4Yi9JR2kxNlhDeEt4RUlsVmF0TTcwaG5pM3NTY08rWTF0c3VEZ3lKY1V5bVVHVnpnRFJmaFNvQ0g2V2JDQXJVS3pTdjlYdGtpSHBwbFNtMFNjcFp2am05OEZ1a1FIR05tbkIyaGZ0a1dBK1lVYmtTODFSQ1U2ek1CUFQyVHUwR2ZNMXhld0d5YytrVm9YL3F2cWY5b05IWVUzZzJTblBvSWZjSmdjNi9xYi9IdkNRT0pyV2NSVGJ4ZTg1dStHalhFa21WQVBrN1hTMTY0WDhka2NINE51M1VwY203WEUxUkV4Z3g4UjhESEZja0hOUkc3bDV1QVE0a3dXZDJBYUJ2WTNIaVRpOEdza2xqNkZablF5R21aaW9OL3dsbHVackNRNHR6NjlqV042alJFNzFiVkRnTERDWkpWcjhUVGRMS1dSdlFGOVIzUXoybS9hQkwrMWUwOU5aeVl6WnBGLzFGQXBQVWhuU2pKc1F4aWdsYjg0TzF4SlRUSjJRdXRiVXhCb0IiLCJtYWMiOiJjMDljZjA5YzAwNDlhYmE5ZjUxNDFiMDVjMTdlYTVlNDg3ZjJhZGMwODBjYTc0YzFkMWE3YzE1NTI2MzEyNGVjIiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 03:35:32 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlpTeDc4cVdJVmdEODNza2FXcXVRN2c9PSIsInZhbHVlIjoiSy9QYTRrT0svcFpsUTE1SkFGNFhjbEtHS2NWWG5ydWljeUt2T0Rsa2UwZUxVbW9rSHRWOWZLVElqS0ZTSm9ZVVp3a0o2bFlaZU5ISjdPN0x6WnBWRm15ZWF2b2RQZndFdXhBd0h6bE8xQWhEZHpuMWZvL09XbklpWkgvcEtGNFpTUml2aGU3MS9XZnd6cWQrcEpRRzRJOUNhaUx3bnoxWmJEYWIxN0VTQ1lxaWVld2Y2V3dGNlZINzZQaklGY0dOTFhIdUFPdDZ6cEYwd3NGZ21wMkxmY2VKakh3TnJkZXUzWVdzLzNVV2F4QitiejBJR3grdFVFN29MZ2oyRXc4eGgwdklKM0MxeE82U1hMR2cyVVppdzViUklWMTBzZU1aaVRyZU8wQjdZeDE3cU1QNXovdkpRQ3NZN2tXMFFqeXFiTjlCQ1Y2Rk92QmtOdmZBT3ZhcmI2YmlMUmxPN2ltVk9UUC9MMWpheXZ6RmNGSldhOUZOZCs3V294bmxLTUdYSmNaMlFudlJYczRsTkdTdC9tYmxENnJ1Nm1BTEdDOXZROVBxOTRyWExGQzVyV3hmcWd6c3RramtLTncwdFd0c3pSSGF3V3BqemRHN2FtUW9weXA1YURmOWVWb1V5N0s5N29uRXByVER3TmZCUVhRMzVWbWcvRXMwZkR1VENoRWMiLCJtYWMiOiJlZDM4MGYwMTI3MzA0MGZmMjE2ODBjZTEzNGNmNWI3ODc1N2YxNDI2ZTkzNDRlYzIyZGJhMDdjMWZjMjgyNTc5IiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 03:35:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlVpOGE0Nks2NkptWkd2MGlOaU1kRWc9PSIsInZhbHVlIjoiaE1rNXd2ZGpCT3BSeTJxSEZGZS9CczlnWTNZK1Rhcmk0V2RNTDcyMXlvUndseXBlU1d0cTJaUnBTWEtyS0JoQjcyaDc3SzhobEU2Zm5oL3pxYmQzK1FnaXJpLzBJWXh0VWlJV1d0Z1dQZHM2cWdsVzFrbkZ6Z3ZkSGNOTFl4Yi9JR2kxNlhDeEt4RUlsVmF0TTcwaG5pM3NTY08rWTF0c3VEZ3lKY1V5bVVHVnpnRFJmaFNvQ0g2V2JDQXJVS3pTdjlYdGtpSHBwbFNtMFNjcFp2am05OEZ1a1FIR05tbkIyaGZ0a1dBK1lVYmtTODFSQ1U2ek1CUFQyVHUwR2ZNMXhld0d5YytrVm9YL3F2cWY5b05IWVUzZzJTblBvSWZjSmdjNi9xYi9IdkNRT0pyV2NSVGJ4ZTg1dStHalhFa21WQVBrN1hTMTY0WDhka2NINE51M1VwY203WEUxUkV4Z3g4UjhESEZja0hOUkc3bDV1QVE0a3dXZDJBYUJ2WTNIaVRpOEdza2xqNkZablF5R21aaW9OL3dsbHVackNRNHR6NjlqV042alJFNzFiVkRnTERDWkpWcjhUVGRMS1dSdlFGOVIzUXoybS9hQkwrMWUwOU5aeVl6WnBGLzFGQXBQVWhuU2pKc1F4aWdsYjg0TzF4SlRUSjJRdXRiVXhCb0IiLCJtYWMiOiJjMDljZjA5YzAwNDlhYmE5ZjUxNDFiMDVjMTdlYTVlNDg3ZjJhZGMwODBjYTc0YzFkMWE3YzE1NTI2MzEyNGVjIiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 03:35:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-636525064\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-286896464 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OxCA9rABwnsUtdJrsRyx2DLpR7CHtGXPC1CLyExX</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-286896464\", {\"maxDepth\":0})</script>\n"}}