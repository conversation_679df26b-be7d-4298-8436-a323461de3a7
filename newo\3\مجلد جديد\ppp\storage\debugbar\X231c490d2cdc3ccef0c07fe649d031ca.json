{"__meta": {"id": "X231c490d2cdc3ccef0c07fe649d031ca", "datetime": "2025-06-21 01:35:41", "utime": **********.904492, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750469740.57902, "end": **********.904538, "duration": 1.3255178928375244, "duration_str": "1.33s", "measures": [{"label": "Booting", "start": 1750469740.57902, "relative_start": 0, "end": **********.713058, "relative_end": **********.713058, "duration": 1.134037971496582, "duration_str": "1.13s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.71308, "relative_start": 1.1340599060058594, "end": **********.904543, "relative_end": 5.0067901611328125e-06, "duration": 0.19146299362182617, "duration_str": "191ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46150336, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.01003, "accumulated_duration_str": "10.03ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.793143, "duration": 0.00495, "duration_str": "4.95ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 49.352}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.825371, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 49.352, "width_percent": 11.565}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.8586879, "duration": 0.00182, "duration_str": "1.82ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 60.917, "width_percent": 18.146}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.88084, "duration": 0.0021000000000000003, "duration_str": "2.1ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 79.063, "width_percent": 20.937}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "OxCA9rABwnsUtdJrsRyx2DLpR7CHtGXPC1CLyExX", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-218678408 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-218678408\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-603856558 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-603856558\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-913164653 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OxCA9rABwnsUtdJrsRyx2DLpR7CHtGXPC1CLyExX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-913164653\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-88211628 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwy%7C0%7C1960; _clsk=1updagk%7C1750469731893%7C7%7C1%7Cj.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImJaWXcwUDB4WXJNSmsrVjM4U2w3R0E9PSIsInZhbHVlIjoidkdmLzRjVTE0TWlOakpPeVkyeU9FN053djN5b25JNTJTcW1qbzlOWmdzaGlzTUhscnpuaWVEdmNJajhhSmsxUkM2R2k0NGF5RVpGcnI2MnpkSnFDTVg1ck1ZdG9qRVJTOUUzWnRmQVhGQ0RWa1BsZlR6UTRvSExXMGllalhWckdZL0ZHVkxTRDQ5V3VRWTVRUnc3MHdrK0tXWldjOUFMUFU2bU8yaTdIbnlFQ3JDOGszZzFKOXg5eWFqWjY0SFFCdFBGc2Fxdk11N3IrY1pyaUpKejlzSHEyK1hRc1d0NFZTamtHUkZTWUZUU2ZSMnBmRG40QnVZdTJYY1hXRWRXc3dhTGVnTC91OWQ1SGZ3RE1ZdVlzWHJrU2hOMjRlTEdmVmtsTmswZktlVmhHVEZUVmlHRE5IUnBnSVVpMWFkaEt5Z3NOS1VUT2hmenJQL3hQUHVmVkoxYTAwV3BQemxLbEdoQTIzNzBGd1NSMGNFY1J4NU5RUmU4OEFnSG9wSlF5Z3VSSjJhRVhuYzJZUmNvamtKMlQrMTBzaHBmaWVaSTM4ajg3cUttR2xFVGJEL0hRc0FoQysrejNRbXhla3Q4a0cwZ3hnZUpTL2xmd3R2ZnNLWmpQamxvNVJXMVBiV0lyR3M1Nit5bzhucnl6LzZkUlN2WFBnT2x5dkFIU1ZKS3IiLCJtYWMiOiI2ZGM3MWUwNDU1ODhlNjNiZjQyMjAyZjU1OGI1MjgwM2MxODc1OTlkOTdhNWM2NjU4MmYxZmUxZDYxNzdiMjk0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InZidDJNZ2VpTjJJWXZKY3QwcDlNelE9PSIsInZhbHVlIjoiQmN6QTRBeUd6b05MUjQ5T0p2WlphejU2eFNDWU1mU0d4VzA4aW5Bak9LNWlVZGtZSkl4KzlWTFREbUE1YWR3OUZBUVJ2K25PNDlDaTViMWhMYlM0WFpKMzcrK0xSRGxnOUxTR0htSVV4YTVpYU5QMDdQTlZkZkpoU0VRNWRlMWhZY01XQ2RSeXJMejFwaG9aV0diOEQvZG5VajF2aHNYRGQ1WlJpTXpNQVc0ZldBZko2TzFaS3FPM0NTNTZOaitQQjAyWXlUdXZJcUxnZ0hJdTJKYTQ3NnJkMmpEQ0RZTGlRdHhwUCtzeUVBSkQxT25tdzBSUStMSzk4YVZsQ1VFOFBTQ2t4ZEM2VnYrRjlVdFpRS2tKcXJYZXlyeHJXZktiVjVtWDVWRnhkeTJKWDFlVVNIVkU2UlE1N2RaVGxlZ0VoalpiQmkyYUh2SFJid3pHWGViakhrRUQ2ZUZHV2VYdkF2enBpdzgrMUhPOGVHZi9aWFhGVHpCbnV3M0NpdWxyNVNka1dXM3M5TUwxUnloaGRNZXBLL2w0c3JscXhUMGt2K0xmQzFKTUlDNE5IZkpCZnVpb2FRMmdxam5DR0JRK2JBb2ZGVXJjNk1vZitEbm1lTTAvOE0xZVF5cjdEUEh5WFpscTlaZjhRMWcwaVVwN0REeitDa2tQZEdDVkpPMWEiLCJtYWMiOiJkNTE4NmI5NjQwYWM2YzM0NWJiNmM4OWY4MzcyNjAxMmNkM2I3NTI2ODY1N2E4Y2MyMDY2MWNjYzdmNTdhNmQ2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-88211628\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1977872819 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OxCA9rABwnsUtdJrsRyx2DLpR7CHtGXPC1CLyExX</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">h4v7KnclY0iOKyPMFu5GxMLqVTN5plmJ6SYNK0tY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1977872819\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1179654346 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 21 Jun 2025 01:35:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IktVZ1JCL3hEa0tHcU5UT09vbzdqRlE9PSIsInZhbHVlIjoienJDS2hCV0ZtR016VFNGRDdZNHhCQ0Evcm0zL0FYSmh2RXNuclB0ekw2UFFIakZ2Unc4MTljSDRXRzJaOUZqUWpDMEtjZVA0d0lXSlJmc1IwYTh1WUozNWJiVUZoYTErUkNRVEVZVGd2S1RiY0E4U01ma09JZHRqcGR1c2wybk9MVFdzNnZNb2l3akVrVitUdzNMaWJxKzZ0T001Y05VNG1uTyt0ZE5oQTJ3aDJGaHVrZUVKMnZieEVHQSs1Y2xJZW1NWFlYNFc5dFdDYnljb1dlZ1JFdlBTUktOYUxUWC9PL1ZraEpnajEwZkY2WDBqMitNWG9yRXhwWXd1b2Rmai9hcW5rRERucDNWVWtyRGRjWjJNeHh5Sk5Tc1gvdHRQYUorR2FsSndRT1gwdUR4WFhtMzVJUXdYaTRYK0lFT1pxM20vekFDSVBsUThqV0Fua3dHR1NWdGZXdkVtSFdsMU9EVE9XK0ZmcmV1a3NEeDJ3NTR2R1RkMWtrQXY1TE9YMC8xWkx5U0hjckV3Y2lVTDV5OVJFNEV3QXJ5VlVHVnQxYzdXbGQzN3hoemdkY1kzWGpyOGV4VzcxSTkvNjJORjBpRFN3UkxldVh0NXpoMENwMndBT2drMVp0SEVXUEVUMFl1a0Z3bHJHeXJUUW85bElmRWljaE0vQXF3K05aajUiLCJtYWMiOiIwODFiZWZhYjAxNTFkNjJlODVjYjViNDYxMmExOGQzYjc1ODkyZDllYjY4MDRjNGM0YjEwYTM0N2IxOWM3MWI3IiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 03:35:41 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IitqUkp5YlhUaHJTWHFkaTdtQ1hWOVE9PSIsInZhbHVlIjoieXl4dmZ2WjIxcGc5aWRoWFcvRjFialVaa1BLd1VIV0FNZTBIL01lbHlJNjFrb0NYUXZwM2RaVllkQnJpQUlDVkQzUTJ0dDVZUEVaSDNVNWRtNlhUcGxGL0lIQ3VCMFRsSGVQUFNzcXJwZkIxODB3YTdLTk1mS2Nuck8rd2FJSkVabXpBdTc1c2k2NzBKOURRYzhEcnBCZ3RGd3ppdWxKWXhOMG1qYys4ajVqeldRRlEzNzQ4eVp0aDBTMHVRcGg4UWtFOWl0S09ZZVQ4WXhJRU5TaWFiUE1sa0NYa1NKUGdjQXI2UGVpcXc1OUtmMTJIQU1LRHdkZC9lTjR0TitHRFczUFFwZ2IrL211clV4eExQM2tSdHNmZE5FeWlreGhlUXZEdm82M01FRlZpQ3ZrY2ZMVTdFdWw4TjdGWFU1Y3hPdTJEUDZIZllibXE4U1Fuc0RKQVhhNjNDRkxZdXVRNFJ1VFdKcHJBa0c5dmwyNC9kVXVGRGFmZGRVVHo1Y0FXMHdhZWg1OXNkaktoY2RISDZLeFN2WFBkNjhZN2ZuUHdUNXFidFFpZFpTRW5QSkVtaDJtT1ZhK01zcEFMTzE3ZHp2UWxsaVZ3ME5FejIyUnhhdEZOditiUUo4ZWJKN0hrNXpZU1hQK01Lc1lQWndJcjRURlBvMzNKRVJKRGZkSGQiLCJtYWMiOiIyNzA0ZjI4NTIzNmU2YzljZGY3YmJiZGUyNmMxODBkMDEwZWE3ZDUzY2VjN2I4OTkwZWNkZDhhYTZkNDZmYmIyIiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 03:35:41 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IktVZ1JCL3hEa0tHcU5UT09vbzdqRlE9PSIsInZhbHVlIjoienJDS2hCV0ZtR016VFNGRDdZNHhCQ0Evcm0zL0FYSmh2RXNuclB0ekw2UFFIakZ2Unc4MTljSDRXRzJaOUZqUWpDMEtjZVA0d0lXSlJmc1IwYTh1WUozNWJiVUZoYTErUkNRVEVZVGd2S1RiY0E4U01ma09JZHRqcGR1c2wybk9MVFdzNnZNb2l3akVrVitUdzNMaWJxKzZ0T001Y05VNG1uTyt0ZE5oQTJ3aDJGaHVrZUVKMnZieEVHQSs1Y2xJZW1NWFlYNFc5dFdDYnljb1dlZ1JFdlBTUktOYUxUWC9PL1ZraEpnajEwZkY2WDBqMitNWG9yRXhwWXd1b2Rmai9hcW5rRERucDNWVWtyRGRjWjJNeHh5Sk5Tc1gvdHRQYUorR2FsSndRT1gwdUR4WFhtMzVJUXdYaTRYK0lFT1pxM20vekFDSVBsUThqV0Fua3dHR1NWdGZXdkVtSFdsMU9EVE9XK0ZmcmV1a3NEeDJ3NTR2R1RkMWtrQXY1TE9YMC8xWkx5U0hjckV3Y2lVTDV5OVJFNEV3QXJ5VlVHVnQxYzdXbGQzN3hoemdkY1kzWGpyOGV4VzcxSTkvNjJORjBpRFN3UkxldVh0NXpoMENwMndBT2drMVp0SEVXUEVUMFl1a0Z3bHJHeXJUUW85bElmRWljaE0vQXF3K05aajUiLCJtYWMiOiIwODFiZWZhYjAxNTFkNjJlODVjYjViNDYxMmExOGQzYjc1ODkyZDllYjY4MDRjNGM0YjEwYTM0N2IxOWM3MWI3IiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 03:35:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IitqUkp5YlhUaHJTWHFkaTdtQ1hWOVE9PSIsInZhbHVlIjoieXl4dmZ2WjIxcGc5aWRoWFcvRjFialVaa1BLd1VIV0FNZTBIL01lbHlJNjFrb0NYUXZwM2RaVllkQnJpQUlDVkQzUTJ0dDVZUEVaSDNVNWRtNlhUcGxGL0lIQ3VCMFRsSGVQUFNzcXJwZkIxODB3YTdLTk1mS2Nuck8rd2FJSkVabXpBdTc1c2k2NzBKOURRYzhEcnBCZ3RGd3ppdWxKWXhOMG1qYys4ajVqeldRRlEzNzQ4eVp0aDBTMHVRcGg4UWtFOWl0S09ZZVQ4WXhJRU5TaWFiUE1sa0NYa1NKUGdjQXI2UGVpcXc1OUtmMTJIQU1LRHdkZC9lTjR0TitHRFczUFFwZ2IrL211clV4eExQM2tSdHNmZE5FeWlreGhlUXZEdm82M01FRlZpQ3ZrY2ZMVTdFdWw4TjdGWFU1Y3hPdTJEUDZIZllibXE4U1Fuc0RKQVhhNjNDRkxZdXVRNFJ1VFdKcHJBa0c5dmwyNC9kVXVGRGFmZGRVVHo1Y0FXMHdhZWg1OXNkaktoY2RISDZLeFN2WFBkNjhZN2ZuUHdUNXFidFFpZFpTRW5QSkVtaDJtT1ZhK01zcEFMTzE3ZHp2UWxsaVZ3ME5FejIyUnhhdEZOditiUUo4ZWJKN0hrNXpZU1hQK01Lc1lQWndJcjRURlBvMzNKRVJKRGZkSGQiLCJtYWMiOiIyNzA0ZjI4NTIzNmU2YzljZGY3YmJiZGUyNmMxODBkMDEwZWE3ZDUzY2VjN2I4OTkwZWNkZDhhYTZkNDZmYmIyIiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 03:35:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1179654346\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1285698776 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OxCA9rABwnsUtdJrsRyx2DLpR7CHtGXPC1CLyExX</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1285698776\", {\"maxDepth\":0})</script>\n"}}