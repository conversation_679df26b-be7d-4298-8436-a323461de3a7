{"__meta": {"id": "X426cf06fd5fe586c7eb22806b9567132", "datetime": "2025-06-21 01:35:32", "utime": **********.805664, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750469731.433955, "end": **********.805702, "duration": 1.3717470169067383, "duration_str": "1.37s", "measures": [{"label": "Booting", "start": 1750469731.433955, "relative_start": 0, "end": **********.588775, "relative_end": **********.588775, "duration": 1.1548199653625488, "duration_str": "1.15s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.588799, "relative_start": 1.154844045639038, "end": **********.805706, "relative_end": 4.0531158447265625e-06, "duration": 0.21690702438354492, "duration_str": "217ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46150360, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.01157, "accumulated_duration_str": "11.57ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.6852899, "duration": 0.00707, "duration_str": "7.07ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 61.106}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.724073, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 61.106, "width_percent": 12.36}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.763637, "duration": 0.00177, "duration_str": "1.77ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 73.466, "width_percent": 15.298}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.783532, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 88.764, "width_percent": 11.236}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "OxCA9rABwnsUtdJrsRyx2DLpR7CHtGXPC1CLyExX", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-633403690 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-633403690\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-168463878 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-168463878\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1151751844 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OxCA9rABwnsUtdJrsRyx2DLpR7CHtGXPC1CLyExX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1151751844\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-819179336 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwy%7C0%7C1960; _clsk=1updagk%7C1750469725331%7C6%7C1%7Cj.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjhIQWJVV0h2Y2VOdlI4aGZyeGpXdnc9PSIsInZhbHVlIjoiOFN1clNhVkhzV1RTZ3pOWmdVZnR3bmZqLzV3eElycVYwczAxaWRhZjNTYzFTbDIrMnBhVDN4amFSNmpKZlgydDAyUmpUR2RvRDZYSGQ2RXBmWTZSYVNDT01BSG1FMi9QcHU5S1N3TXFROFdtTTNBSjk5V3FRdWtoak5CWnpuRzdzTVpQb0xqWUdaaEUrcnVOcjVxN2E0OHVSQzR6MWxJODFQU3BWekY0SVhraWRQa2pnczJFS0l2WXdEcEJockt2ZFlPWnNmMm5rUFpCYTIrb3FHN0V2WXZ4SFB3VWhYZU9MRnFRczFZSEVMdmJ3VmdQRklnZEZtZXgxa2xHYmE0c1JHOEpqeXRGeWFacTVmVE9BQlBRVld6QUNsQkJWQWlZT1o2WlZPU3BCM0NxNi81dkJ2MGs2R1J6MkI5N3NNN3lyMFdIOXBvODF5VStnbjEwRTdUc1JYVHhITTlTN0FDUlZDZldRUmxOMHpIMG9UVk0zRm1qaUhQMmdVbWs4dFN1R2swSHR3T3BJazNvQ2syODBJaTYrM25VZllmSlVGWkFQT3QyeFFJcUNrZ0xmeDl2YkFvNDdmZVNseFdqUS9QRzcxY1RDTzZDV2NSRnJhOFpEemgyUXRnTTd0WFJFY0FRUG9TSlpTWVNJTUh4RDdOUUV4ZTB5M1o4bVFUdjRlK1kiLCJtYWMiOiIwOWJmMzE2MjhiNTUzMGNjNmQ5MDg1ZDc3MWQ3YTQzN2E5YTkzZWYwNjgyY2M1NjNmNzk5MTRlMmEwYjliOWRiIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImNmUVo1bE1hZTJtQU5rRVdJd3JvclE9PSIsInZhbHVlIjoiQ0I5bE54WUFMSlloRGgydkVPaVQ4QWM2ZWl4VEdYeEZkekRaYU1DUjJ6eFJhM1FWSERYVTdQdVVEOVlPQWRpT3V6RXFZS0ZqTUpzcHlzeHlYK3JzR0JsL2NYbmo1VU9qMVBhSktjL2M1ZWh1NWw1S2pVUGZJTFRvRHFKYjI2a3Z4QU1wQ0VNOGFjWDIyZXdPK01JMEk4L05kZ2FzUXd3ZkZmelAvWXd4N0E5YlFTKzJGT25neHo0TnQ4aFl3MkdaWklMak00ZCtEdEtqeVlMRW91UzArNG9VOWV1bll0UzRGenRyQlNJdGM5a25PUlFkTnRMNVdtdm14TGF2cmJiaVhCV0Q2c3FWZUNhRlM2UEpiNkE4R05DQVprK21iNjVFa1hKeHVpYW1aa2szT2d2WGlWcEtZZjhCSHFYUlZkL0VjUkc1K0VUOEhoUENudGxwT2NaZDZkVTIrbWQyV0pqUlUyQ1Nkb2k5SCtjYjIveXQ0c3FoVXh4bXIzSUdWNGtzc1hzbTByWnBDWEhvMk5ya0VtSXVjWU9pd1BFWDJPcHNZQU5CeGxsdWkzOE5WZVNuZnlhcHpqWHhDR1pmY1AwR1lVVzFabndSbUhadjkvM3hRT1JKc1JpZ3Z5Zi9oVXduWXhSMVFPamd0TWgwcGl2L1dxblRBUXVaMWRZNG5takIiLCJtYWMiOiIwOTViZjk4ZDUwMGM4YmNjYTM2YWIxZjhjOTRlOGJkYjk3NmFiMTJmYmMzMjFmYzg0MTA2OTUzNzg4Mzk3MjczIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-819179336\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-535542865 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OxCA9rABwnsUtdJrsRyx2DLpR7CHtGXPC1CLyExX</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">h4v7KnclY0iOKyPMFu5GxMLqVTN5plmJ6SYNK0tY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-535542865\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-689816612 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 21 Jun 2025 01:35:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjdkbTFHUXFZS0xYSlBWalJDbGc1Z1E9PSIsInZhbHVlIjoiZ1UrS3NPaXR6RnFVM2tCSGRsVjhSK2p6N3RGN09lQmdYYnVQNmRRSXV2VVJkZ0YwKzlzNitaU25jSkFIT3BKQjEyR3FJSk5NcTV2b2RDcXRZUVRXaERyaVFqYTBBNktIREJhTUZ0SVdEQVl6NnNKbFpmOE4yNTlpdmFLR2IxeG02OHFBcEVjQUppMFhLWFF1alhxeUNuY1dUb1ErRTdzWVU1V1pNNHpBWFlPZDVOaEtyQ1N4bFhLM3VSU00yQ1BjbDNIWlNCU2IzaUpFandVNExpTmhJT0Q5SWpCU3hwUWg4YkpVelZhZ3UzOThsUFl2TFRDOEtwU0FmRGRxQ0ZmYWErd1hrdDZCcDhub1pSVzJpUlJ0MTlDdktJSlVkd3pWeHA2S0FSR25OZzdYR2M5alUrSlNDbzI5ODM3b1RGYkg2cy9XSnRWVkNQSkt4MUU5UnZoa0g1ZmRvWHJRQ0Y1d1NKSXRNOENPWnB4aUZFdE5sbjVwMU9BOC9DS21iZ29JZlZERjlBckZ3bW94MEhNL3RJclNwSnZ1dEZObVZFTWs3TFpMOXNzTXNiRVY5Q3djZThlZ25vUzlxTEM2MkVLcmZES3Q5bkhmVjhHclpJaUxvbVVSempKc2JQUHJXVzFpaFpEenVBRmowTEdLYzNxM1V0NDg5OFQ4R0tNeFBMbDgiLCJtYWMiOiIxYjI3MTFkMzQ2ZjM4ZjM4MWYzNzM1OWU5NTFlYjBmMTYyOWYwMzNmMjM1Njk2OGQ3Y2ZlMzFhOWI5N2UxYzFjIiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 03:35:32 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Im9NNUt0SkZZYmRtZzVTTllsSE9mVWc9PSIsInZhbHVlIjoiN3JBbWg5WldHWUNZSkpBdHh3R01XSGplVVRnQ0xoYVM1WEE1bzhOUGdNRzdJeWJ2RUk1cWZxaEI4ak1zTnFPNUxrV3huWUVEZGxQWmF6eHB6a0xJQ21nc214UXpFa1RQVitVU1ZxZVlRcHJocWJuYWFnajkxSHdsZHJqelYramJETDNpUnVYRmNwZk5NUzFCV2M5WkxWNWtGM1F0ZGNzN3dyVWNMR1BQZ0VDSDZweUdWNzhPYjhLL1FTN2h1ejlHbHpIbXZIR28vRWFGZVlpYW0yUDA1dU1wVHUwYkczVWg5Q0JiK2pocjQwUFMxRWRSZWRFV2htQ1RjSWdpOFJubHgyWHM4aDh4MENuc3lQR05MZWN2TUx1WTZmbjZrTDFiMXlLelp2a0tXNWdJNjFkMEVMNHYycWViR2pjVmhYVlhrQzduVVplOXNQMzNqVFJTNnVkSnhRVmV4RVpKWk5wb0ZmckxPcS9WcDRiQjdyb0F6SUZjeUtJQjRScGNqbHFaSVl2Q3dzclhtMzhDeUJkcGgxYnFWS2FWY3dQUkdNdkN6QzRLQVFIQWtmVko5bmw1VUV6WERSRkJkS0U5dmtMK25zb1ZUb3dkdlNpZG8vOHpsZW9yY1U2ZjBSRGhqM0JyVWdKT2ZXWjE4cXQxaFd6T3g0RWx4ZS9CcFZNczVFL2YiLCJtYWMiOiJjNTNmNjlkMjllZGUyZjNkZTRmZWIzY2Y5YTc1ODAxOGJhNjg5ZmJkNDVhYTEyMTRkOGRhZjE5MTZiNDk3NDkzIiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 03:35:32 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjdkbTFHUXFZS0xYSlBWalJDbGc1Z1E9PSIsInZhbHVlIjoiZ1UrS3NPaXR6RnFVM2tCSGRsVjhSK2p6N3RGN09lQmdYYnVQNmRRSXV2VVJkZ0YwKzlzNitaU25jSkFIT3BKQjEyR3FJSk5NcTV2b2RDcXRZUVRXaERyaVFqYTBBNktIREJhTUZ0SVdEQVl6NnNKbFpmOE4yNTlpdmFLR2IxeG02OHFBcEVjQUppMFhLWFF1alhxeUNuY1dUb1ErRTdzWVU1V1pNNHpBWFlPZDVOaEtyQ1N4bFhLM3VSU00yQ1BjbDNIWlNCU2IzaUpFandVNExpTmhJT0Q5SWpCU3hwUWg4YkpVelZhZ3UzOThsUFl2TFRDOEtwU0FmRGRxQ0ZmYWErd1hrdDZCcDhub1pSVzJpUlJ0MTlDdktJSlVkd3pWeHA2S0FSR25OZzdYR2M5alUrSlNDbzI5ODM3b1RGYkg2cy9XSnRWVkNQSkt4MUU5UnZoa0g1ZmRvWHJRQ0Y1d1NKSXRNOENPWnB4aUZFdE5sbjVwMU9BOC9DS21iZ29JZlZERjlBckZ3bW94MEhNL3RJclNwSnZ1dEZObVZFTWs3TFpMOXNzTXNiRVY5Q3djZThlZ25vUzlxTEM2MkVLcmZES3Q5bkhmVjhHclpJaUxvbVVSempKc2JQUHJXVzFpaFpEenVBRmowTEdLYzNxM1V0NDg5OFQ4R0tNeFBMbDgiLCJtYWMiOiIxYjI3MTFkMzQ2ZjM4ZjM4MWYzNzM1OWU5NTFlYjBmMTYyOWYwMzNmMjM1Njk2OGQ3Y2ZlMzFhOWI5N2UxYzFjIiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 03:35:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Im9NNUt0SkZZYmRtZzVTTllsSE9mVWc9PSIsInZhbHVlIjoiN3JBbWg5WldHWUNZSkpBdHh3R01XSGplVVRnQ0xoYVM1WEE1bzhOUGdNRzdJeWJ2RUk1cWZxaEI4ak1zTnFPNUxrV3huWUVEZGxQWmF6eHB6a0xJQ21nc214UXpFa1RQVitVU1ZxZVlRcHJocWJuYWFnajkxSHdsZHJqelYramJETDNpUnVYRmNwZk5NUzFCV2M5WkxWNWtGM1F0ZGNzN3dyVWNMR1BQZ0VDSDZweUdWNzhPYjhLL1FTN2h1ejlHbHpIbXZIR28vRWFGZVlpYW0yUDA1dU1wVHUwYkczVWg5Q0JiK2pocjQwUFMxRWRSZWRFV2htQ1RjSWdpOFJubHgyWHM4aDh4MENuc3lQR05MZWN2TUx1WTZmbjZrTDFiMXlLelp2a0tXNWdJNjFkMEVMNHYycWViR2pjVmhYVlhrQzduVVplOXNQMzNqVFJTNnVkSnhRVmV4RVpKWk5wb0ZmckxPcS9WcDRiQjdyb0F6SUZjeUtJQjRScGNqbHFaSVl2Q3dzclhtMzhDeUJkcGgxYnFWS2FWY3dQUkdNdkN6QzRLQVFIQWtmVko5bmw1VUV6WERSRkJkS0U5dmtMK25zb1ZUb3dkdlNpZG8vOHpsZW9yY1U2ZjBSRGhqM0JyVWdKT2ZXWjE4cXQxaFd6T3g0RWx4ZS9CcFZNczVFL2YiLCJtYWMiOiJjNTNmNjlkMjllZGUyZjNkZTRmZWIzY2Y5YTc1ODAxOGJhNjg5ZmJkNDVhYTEyMTRkOGRhZjE5MTZiNDk3NDkzIiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 03:35:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-689816612\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-291480125 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OxCA9rABwnsUtdJrsRyx2DLpR7CHtGXPC1CLyExX</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-291480125\", {\"maxDepth\":0})</script>\n"}}