{"__meta": {"id": "X7b8676da3bfdbd00ec744292470cdcf5", "datetime": "2025-06-21 02:44:07", "utime": **********.486778, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750473845.000631, "end": **********.48682, "duration": 2.4861888885498047, "duration_str": "2.49s", "measures": [{"label": "Booting", "start": 1750473845.000631, "relative_start": 0, "end": **********.31504, "relative_end": **********.31504, "duration": 2.314409017562866, "duration_str": "2.31s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.315069, "relative_start": 2.3144378662109375, "end": **********.486825, "relative_end": 5.0067901611328125e-06, "duration": 0.17175602912902832, "duration_str": "172ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44660616, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.01776, "accumulated_duration_str": "17.76ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "guest", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\RedirectIfAuthenticated.php", "line": 25}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.439424, "duration": 0.01776, "duration_str": "17.76ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "OxCA9rABwnsUtdJrsRyx2DLpR7CHtGXPC1CLyExX", "_previous": "array:1 [\n  \"url\" => \"http://localhost/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-425320710 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-425320710\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1462109243 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1462109243\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2120504106 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=nups8l%7C2%7Cfwy%7C0%7C1960; _clsk=1updagk%7C1750469741656%7C8%7C1%7Cj.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IndqQ29qem5oNDlTV0NHT2RURmluTEE9PSIsInZhbHVlIjoiVjJIVnV4MjlSYnBMNElib2gyK2NQdC9GRSt2OFVWb3p2ZWUwWHlEczZrblhmYmMvamdua0tZYXBrSjZWT2NYTG9iTHNLTDN3UVdzU3R2cFNvQVlwbTI4YTZaSElwcTNWaHo1YldlQ01ZUnZOcVRBMXU5YUhVL0NNZUl0RFhLWjY1Zys1MTVVaWkzSmpWNTl6VkVuWkdtTkNWcjdZSmhKc0toTG96SGZXdVBsMzNmTDAxaGFBK3MyNDltRzh4bzN1V1haR1gvYy9icVFIQ1IvNjIrMXZKeEJPaFJLREVJU2NlUzJkaG94Z2pmcnFGNGd0RWU4K1ptTER0V3V6K3d3QlcvbEloYW1xRjVNd1lOSm9QZW9hVG1CbnZ3SlZYZGZrdDYzeFVaWHpJNHpQWVRvVEFQaUd6VGtPWm1qOE1RcVAyZ1FTdmpyVURKdFBUZnBPanZFR0FWOGVDK01uZjJ0T1RRWFNaRE5xVVJzSmVXOG5vQ25RTUlNYnJOZ1B2RWR1V2t4bTdBaEFmdW9CbnM0YTdhS3pwSVNzeGVNZW9HcVRQM29OSGIwS0lZOVVCc2RwZ0xiOGFMdWs2QVcwNGIxenVPckk3OUZjTWErOFU4V0ZLUWlJQkhFL2RhNVE1VFZXR1J1YkEveVp0Rll2Q3ROMFMvejBUdHdRL2RBSStidWkiLCJtYWMiOiI4NzMzZmQ4Njg2MGQ5Y2VjYzY3OWFiMzdhZDQwNmE3NGQ5ZDllY2M5N2U1ZTQyZGE1MWEzOWMyYjllN2FmNDhjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkFueitnaEhVZ0gzdVVORTh6ckJ3c2c9PSIsInZhbHVlIjoiU0hZWlhiZkRweSs4T3pDcm5NRk1SYXZ2Y3Q4aGJ2NUlDVDZONlFhM05ZT2RIK0NHUkwydFV6bUNYK2c3bnlSK3Q5SU4rdi9aZlpoaGt2QngxMm9tVnJFVnJNU3RNcHlaaVA1dWgxMTJNMEw2K251a0xOVGpFNDV0aExhOVIxeXErMGJUUmFobXVRTDZNcklkQlAzVmlSVENLZFVWOTJoaDZCWlIxd0JONlZLRmgzdzFseVJ2RGs0ZWZHb29tL3NOd2plNnhLU2tlbElzNlhlaklIMkM5NnFoZGpyTUVzbERrbmdVWU53TVZyQmlQRWh1Nkl5b211aGY2ZDFPNXBwbUNZQ3hiZEhKclRseXJmcnRBbGZ3cmxVWkJSYWJiR2F0RlREdmdvZE9uV25tWncwaWYzaEpuVWtFazVPNmFRR3lHM0xIM2ZiNnRlbUJCaWhQTU16K2RKZ01PcDdYdDhRWXZFbFIzQVp5RDd4YWlGZS9UVkt5ajlGSGtINXZsVlUva3lERm1jT0lIdS92WXFZWU1pNlpaK2xVRDB2UWtxQ2Y1V21aakpGbTlZWG5XUUw3dnA1VzNCYXBqQTFYQVZUdXkyZ1hrSWVsZmlkUjNrNUs2MFRRcXlpL1hSYVFUMTgxOGRrbHNCVkRNWTdtUGNlVHdGZUU5czV0RUJoSytCM0ciLCJtYWMiOiIwNTdkNTEzMmQ5NzAyNzM0MTQ2ZGY0YWE2NjQ3ODczMzA0NWJkOGIyMWFlMzNiOTI0YjQxZGVkYzU4NTE4YzNmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2120504106\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1858614500 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OxCA9rABwnsUtdJrsRyx2DLpR7CHtGXPC1CLyExX</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IqIQb9J95tflkp3hIkSidGYurblANYNn28mhR1bv</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1858614500\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 21 Jun 2025 02:44:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkZpcS9BRjVwa211bTFXaG5ZWjNkWWc9PSIsInZhbHVlIjoieVhiamlFWmhJdVZHWjlLaERYQ2J3eEc1VXdZVXh3R0RocUpPbXE5UHJXZWtWWmNPaG5WeWFWT3hYOXFaWEtoRy9IQzZ1K3owVDF6SFRFWkpZRVNRTi9XeVZlRnZpM2ZvOXltcUEyYk1QS3lUZDRLdjhXYVhMN3JVamVzaWF3OFJzRURpMWlWaWtxNXI5NENDMXFJWlFzU00yZldGcWNqNlZzaDZaU2lkWGxJelVvbzdCemhvem5Id21iVUF4MmxwZUNpaGxiYnZoczJ1QmZPTnZYNHkvS2xpYXVrdEgzRS9KeUx3L1MySnBJR0t4b0lqcW5zUUFyQUpmT3NyTFkyVnU1UE9MN0JzM0sxRDU1OXZGWGtUNzBaMHd1c2Z0OGxCWlRzblk2T2RvcDcwS2VGaGZuRmRUUExxU3dEQnFQUGdWL2U0NWNlQlhxdEZyVGVIWUh0R04xUEsyNkw2RzVtRWZyZlloc21RVkprVEdQUi8yWjhiQlhibmJCSFljMjRhNXhscUlCdmRlWjAvdGZ4TFFiT2Y0eE5TbzZ2NXYvTmFXUVRjYWpUSFBTS2lFdmQ0Ykl2SXNoRGxla3huTUVGVktZZHNFYWtPSEVFdnVjVkRzRkR3Y3RlY0N5aGEzUWdrdTdQdWJaT3NGNEpTRmJSOEZub01pK1MzU0sxVFJTSjciLCJtYWMiOiIyNGJjNzkzZDk4YWU1MGZmNGZlZWQ5OGU3ZmFmNmI2YzYzMjZjMzM0MjA1OWU2NzQ2ZmJhNDk3Y2U5NTJkZThiIiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 04:44:07 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IllaVit5WldFUzYydFE4emNVTlNtUlE9PSIsInZhbHVlIjoiRDBWbVFhMDVjN2NOSEZSak9xU000azEvNmNCRUU4VllENkJCVWRBZkdsbGpuVkNGNG4yMk5MNmJNMHNKdmg2V2R3NXEvdm1rRTRYTUpqOE16OTI2N3Q5TldUazBLaXcvMXdOWjU0OHNrMW43R0JEMml2TlhVZTNMWXRyZE4zOXdiZmIrNndQRmgwYUprZmhtbTczbGpORTV4YzlNL0Nlc1hqUEc2YnZTdTJwcWlsRW56K1hubkxNMUQvN2tsRCtEd2RNcXB1NEhOZm9BUWc3V1BLUHdHU0hzZk4xckpiOTdiVExHd1gzSTVOK21iZjgrV25uSFN3SFoxNFZtWGI5dUpTMDE2by80dGdaWWFGdkIxeEJqb3JuOXhsSVo5RGVXeGRSRjdYUC9JNEtmYTh1eEtPNmxsOWxQZ3ZMYkJMQlJGVzIwNHNWMis3blJmUmVROWt2SkJ3M1Nxa25sa2d6cjVMc2I5RWFSaC9GNmFSK2sxejQ2c3dwR3JmKzRoRjUvWTM0R3puQkNlbks4SkhpSENiSk1ZajhPY1lHQWptbmcxeFMrYXRkazc4WkNzaTNER3VTQWRjU3BRZ3dvQklTemNJNkg2OHh1alBwNm5ra0FUdnd5ZVpraTNvNkR1ZStqQStJL1JITUFZR0VEa2JTV2h3aHAraVRNa3c0L0JqZ1QiLCJtYWMiOiJkYTYzZjRkZmNmY2ExY2MyNDIwMzUyNWMyYzUyZWFhYjEyMDgxMjhlNDQ2YTQ2ZGE2ODMwMWEwZjc3ZTFkZTIxIiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 04:44:07 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkZpcS9BRjVwa211bTFXaG5ZWjNkWWc9PSIsInZhbHVlIjoieVhiamlFWmhJdVZHWjlLaERYQ2J3eEc1VXdZVXh3R0RocUpPbXE5UHJXZWtWWmNPaG5WeWFWT3hYOXFaWEtoRy9IQzZ1K3owVDF6SFRFWkpZRVNRTi9XeVZlRnZpM2ZvOXltcUEyYk1QS3lUZDRLdjhXYVhMN3JVamVzaWF3OFJzRURpMWlWaWtxNXI5NENDMXFJWlFzU00yZldGcWNqNlZzaDZaU2lkWGxJelVvbzdCemhvem5Id21iVUF4MmxwZUNpaGxiYnZoczJ1QmZPTnZYNHkvS2xpYXVrdEgzRS9KeUx3L1MySnBJR0t4b0lqcW5zUUFyQUpmT3NyTFkyVnU1UE9MN0JzM0sxRDU1OXZGWGtUNzBaMHd1c2Z0OGxCWlRzblk2T2RvcDcwS2VGaGZuRmRUUExxU3dEQnFQUGdWL2U0NWNlQlhxdEZyVGVIWUh0R04xUEsyNkw2RzVtRWZyZlloc21RVkprVEdQUi8yWjhiQlhibmJCSFljMjRhNXhscUlCdmRlWjAvdGZ4TFFiT2Y0eE5TbzZ2NXYvTmFXUVRjYWpUSFBTS2lFdmQ0Ykl2SXNoRGxla3huTUVGVktZZHNFYWtPSEVFdnVjVkRzRkR3Y3RlY0N5aGEzUWdrdTdQdWJaT3NGNEpTRmJSOEZub01pK1MzU0sxVFJTSjciLCJtYWMiOiIyNGJjNzkzZDk4YWU1MGZmNGZlZWQ5OGU3ZmFmNmI2YzYzMjZjMzM0MjA1OWU2NzQ2ZmJhNDk3Y2U5NTJkZThiIiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 04:44:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IllaVit5WldFUzYydFE4emNVTlNtUlE9PSIsInZhbHVlIjoiRDBWbVFhMDVjN2NOSEZSak9xU000azEvNmNCRUU4VllENkJCVWRBZkdsbGpuVkNGNG4yMk5MNmJNMHNKdmg2V2R3NXEvdm1rRTRYTUpqOE16OTI2N3Q5TldUazBLaXcvMXdOWjU0OHNrMW43R0JEMml2TlhVZTNMWXRyZE4zOXdiZmIrNndQRmgwYUprZmhtbTczbGpORTV4YzlNL0Nlc1hqUEc2YnZTdTJwcWlsRW56K1hubkxNMUQvN2tsRCtEd2RNcXB1NEhOZm9BUWc3V1BLUHdHU0hzZk4xckpiOTdiVExHd1gzSTVOK21iZjgrV25uSFN3SFoxNFZtWGI5dUpTMDE2by80dGdaWWFGdkIxeEJqb3JuOXhsSVo5RGVXeGRSRjdYUC9JNEtmYTh1eEtPNmxsOWxQZ3ZMYkJMQlJGVzIwNHNWMis3blJmUmVROWt2SkJ3M1Nxa25sa2d6cjVMc2I5RWFSaC9GNmFSK2sxejQ2c3dwR3JmKzRoRjUvWTM0R3puQkNlbks4SkhpSENiSk1ZajhPY1lHQWptbmcxeFMrYXRkazc4WkNzaTNER3VTQWRjU3BRZ3dvQklTemNJNkg2OHh1alBwNm5ra0FUdnd5ZVpraTNvNkR1ZStqQStJL1JITUFZR0VEa2JTV2h3aHAraVRNa3c0L0JqZ1QiLCJtYWMiOiJkYTYzZjRkZmNmY2ExY2MyNDIwMzUyNWMyYzUyZWFhYjEyMDgxMjhlNDQ2YTQ2ZGE2ODMwMWEwZjc3ZTFkZTIxIiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 04:44:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OxCA9rABwnsUtdJrsRyx2DLpR7CHtGXPC1CLyExX</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}