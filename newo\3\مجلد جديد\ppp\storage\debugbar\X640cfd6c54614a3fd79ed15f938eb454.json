{"__meta": {"id": "X640cfd6c54614a3fd79ed15f938eb454", "datetime": "2025-06-21 01:33:35", "utime": **********.725045, "method": "GET", "uri": "/", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750469613.10483, "end": **********.725081, "duration": 2.620250940322876, "duration_str": "2.62s", "measures": [{"label": "Booting", "start": 1750469613.10483, "relative_start": 0, "end": **********.436303, "relative_end": **********.436303, "duration": 2.331472873687744, "duration_str": "2.33s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.436353, "relative_start": 2.3315229415893555, "end": **********.725086, "relative_end": 5.0067901611328125e-06, "duration": 0.28873300552368164, "duration_str": "289ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44442792, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@landingpage", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=59\" onclick=\"\">app/Http/Controllers/DashboardController.php:59-74</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.06689, "accumulated_duration_str": "66.89ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\DashboardController.php", "line": 66}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.610028, "duration": 0.06689, "duration_str": "66.89ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "bZXnNhrFZQoeHA6NDip6U19oFF9o2UjGwMJf9tmQ", "_previous": "array:1 [\n  \"url\" => \"http://localhost\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1684347246 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1684347246\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1709175811 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1709175811\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-257781663 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"194 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfww%7C0%7C1960</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-257781663\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1938645189 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1938645189\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1132639849 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 21 Jun 2025 01:33:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkpiRUVuUXM1NVpkdFdYd2NLZWk2cVE9PSIsInZhbHVlIjoiKzV6bTN2Yk9nVG1ja21CQ0xFUkxWRmdDRDdMNzE4L3gyRW1wNGlaR0ErN05YdmQ3aVl0dG16d29KOTNvbko5aVhkanZhQThpeE9IRVF2MVFBaFcvUjlBVUdVNEpUbnVNUWx4Y1JJZWVwNUVJdHZmSWtFT1VBMjBmSlpiRTVZUDhFRHpIeS9rWmd3YllnS1lqQWxrNHFJdlN3MmYrZ2s0RGhkbEFSSm1xRXRaa0Vtamt0NDB3cTlUZ05tbndaRE9SUlA1Wk1CRWFQVkFQWHRqaHJIc1Zkd1Z5Wlh3dGErK1NFekZpV3ZWRklyV3VBM3B1UmxYYzBVamgxZ3cxbFAwSUw3YVN6TEl0YmUyY0tJUnhWYlZIZWhhYllxSTVOZDF5anl2VzVBa3lUQ1VaNngwOUJWb0dPdmxhVFo4dmI2VmhkSUhyK2MzaGZDWUt4SDFoQkFZd2NCd0hQVU80Zk1HQ3hWZ0lHS2dzU1hBc3VtVXBYUkJ6L1ZhYXlxUFpPcXowbm5GN0tlbHNPZ2lrdFJySFBPTHlmVCt6VzdFZ00vWUNCdFZ1dk5PMWd3M0VVZnhCZGxqLzNRaUxpcXZ3T05TUjgvcUYzaFN4MFJpYVphWng0YVZFSkw0MGpSWWJLK3JrTC83eHNJalNqbkRaZ2RkQWk1clhuUHRVWVZINHdJM3kiLCJtYWMiOiIyMTZhZmRjMTM0MzkwNjFmMjhlZWNlZjAzMzk3ODFhOWZkMDljYzcxNGFhZWRlN2MxNDAxN2U2M2NiOWExNDM3IiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 03:33:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkZ5MFh2U0FKRzlZSjVpZnN6OVBTaVE9PSIsInZhbHVlIjoiSUR3c05iR09nS0pCTVAwWXhHVG95Tk9jYmdRRGZBU2hKLzZ1dGhkTTNvaW1wM3lqbjFpVWhqRnIwNTEvVHQ5bWY3dnBEK1hHL05YM1pQOVpJQzRpWUZBaEN0NTFlV1FGUTFzZnNJL0lTMk9SNFhPQ1JRcjI0cmxMSVJHRzZIU0NzaW83a3huKzZYc3pmMGxyN1BmUkJZVzlxY0M5YVJEQkcwbTNocmxWUUhud2VxelpPeUpKekhYNmM2QnBOMHkwSVlrWnRtZk5lTzRGVUdualRKUmhUUDl5bDY0MlIvQ1c4V3pNREROLzBycDhQS3FvS2lkanU2VVZYdGtKbVM1V2xlSFVnL2cxQ1pKd2VCQ3EyNjhMMTBKLzZPRzc4S3ZVd2M1SE8xZ0lXczZCbmp4MmVsT1dKdHcyaXEwL1NvMTkyNFQxbnhUNm0zOXJuaFFoV014ZnZRZHp0eXFDQ2h2MFZPZ0VqYXQxNG8wd3VpQndjQmpLNHF6K1hpVGtrVjZyTHVXdlhaUlQ1TDlxOGRYaTNtTjZrSjRFWERSZk45REFiY3dmdUxKTVl2Nkl3VlVMVkk5blJ2TnJsSVc0enVyQlZmN1RxVGxVcGc1SkdPK0Ercld0NTVwRUlJdVRySnN3R1VWNW9ZdGlSKzh3R0k4azNkaTBNWStMSkVrcktWbXgiLCJtYWMiOiI5Yzc3OTExZDc0OWRkZDk4MGZkMzdkNWQ1YTAyZjhmYWZiMzlmZGI2YzA3Y2I1NDhhZDA1YTNmNTA5ZjJkZmQ4IiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 03:33:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkpiRUVuUXM1NVpkdFdYd2NLZWk2cVE9PSIsInZhbHVlIjoiKzV6bTN2Yk9nVG1ja21CQ0xFUkxWRmdDRDdMNzE4L3gyRW1wNGlaR0ErN05YdmQ3aVl0dG16d29KOTNvbko5aVhkanZhQThpeE9IRVF2MVFBaFcvUjlBVUdVNEpUbnVNUWx4Y1JJZWVwNUVJdHZmSWtFT1VBMjBmSlpiRTVZUDhFRHpIeS9rWmd3YllnS1lqQWxrNHFJdlN3MmYrZ2s0RGhkbEFSSm1xRXRaa0Vtamt0NDB3cTlUZ05tbndaRE9SUlA1Wk1CRWFQVkFQWHRqaHJIc1Zkd1Z5Wlh3dGErK1NFekZpV3ZWRklyV3VBM3B1UmxYYzBVamgxZ3cxbFAwSUw3YVN6TEl0YmUyY0tJUnhWYlZIZWhhYllxSTVOZDF5anl2VzVBa3lUQ1VaNngwOUJWb0dPdmxhVFo4dmI2VmhkSUhyK2MzaGZDWUt4SDFoQkFZd2NCd0hQVU80Zk1HQ3hWZ0lHS2dzU1hBc3VtVXBYUkJ6L1ZhYXlxUFpPcXowbm5GN0tlbHNPZ2lrdFJySFBPTHlmVCt6VzdFZ00vWUNCdFZ1dk5PMWd3M0VVZnhCZGxqLzNRaUxpcXZ3T05TUjgvcUYzaFN4MFJpYVphWng0YVZFSkw0MGpSWWJLK3JrTC83eHNJalNqbkRaZ2RkQWk1clhuUHRVWVZINHdJM3kiLCJtYWMiOiIyMTZhZmRjMTM0MzkwNjFmMjhlZWNlZjAzMzk3ODFhOWZkMDljYzcxNGFhZWRlN2MxNDAxN2U2M2NiOWExNDM3IiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 03:33:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkZ5MFh2U0FKRzlZSjVpZnN6OVBTaVE9PSIsInZhbHVlIjoiSUR3c05iR09nS0pCTVAwWXhHVG95Tk9jYmdRRGZBU2hKLzZ1dGhkTTNvaW1wM3lqbjFpVWhqRnIwNTEvVHQ5bWY3dnBEK1hHL05YM1pQOVpJQzRpWUZBaEN0NTFlV1FGUTFzZnNJL0lTMk9SNFhPQ1JRcjI0cmxMSVJHRzZIU0NzaW83a3huKzZYc3pmMGxyN1BmUkJZVzlxY0M5YVJEQkcwbTNocmxWUUhud2VxelpPeUpKekhYNmM2QnBOMHkwSVlrWnRtZk5lTzRGVUdualRKUmhUUDl5bDY0MlIvQ1c4V3pNREROLzBycDhQS3FvS2lkanU2VVZYdGtKbVM1V2xlSFVnL2cxQ1pKd2VCQ3EyNjhMMTBKLzZPRzc4S3ZVd2M1SE8xZ0lXczZCbmp4MmVsT1dKdHcyaXEwL1NvMTkyNFQxbnhUNm0zOXJuaFFoV014ZnZRZHp0eXFDQ2h2MFZPZ0VqYXQxNG8wd3VpQndjQmpLNHF6K1hpVGtrVjZyTHVXdlhaUlQ1TDlxOGRYaTNtTjZrSjRFWERSZk45REFiY3dmdUxKTVl2Nkl3VlVMVkk5blJ2TnJsSVc0enVyQlZmN1RxVGxVcGc1SkdPK0Ercld0NTVwRUlJdVRySnN3R1VWNW9ZdGlSKzh3R0k4azNkaTBNWStMSkVrcktWbXgiLCJtYWMiOiI5Yzc3OTExZDc0OWRkZDk4MGZkMzdkNWQ1YTAyZjhmYWZiMzlmZGI2YzA3Y2I1NDhhZDA1YTNmNTA5ZjJkZmQ4IiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 03:33:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1132639849\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-406867141 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bZXnNhrFZQoeHA6NDip6U19oFF9o2UjGwMJf9tmQ</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-406867141\", {\"maxDepth\":0})</script>\n"}}