{"__meta": {"id": "Xb93150ae5da84c7dc029fd749cc79eb7", "datetime": "2025-06-21 01:34:55", "utime": **********.043687, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.621536, "end": **********.043716, "duration": 1.***************, "duration_str": "1.42s", "measures": [{"label": "Booting", "start": **********.621536, "relative_start": 0, "end": **********.849585, "relative_end": **********.849585, "duration": 1.****************, "duration_str": "1.23s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.849603, "relative_start": 1.****************, "end": **********.04372, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "194ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00907, "accumulated_duration_str": "9.07ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9453812, "duration": 0.005889999999999999, "duration_str": "5.89ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 64.939}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.9807022, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 64.939, "width_percent": 16.648}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.0200732, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 81.588, "width_percent": 18.412}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "OxCA9rABwnsUtdJrsRyx2DLpR7CHtGXPC1CLyExX", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwy%7C0%7C1960; _clsk=1updagk%7C1750469691324%7C4%7C1%7Cj.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkhJQ25ySE4rSUY2OFR6MXZQMWN4ZkE9PSIsInZhbHVlIjoiRDVNMU9KT2NIQlBod2JxWW40WUg3TkRMYjVrYlcrejZwcDZvemhXY3d3N3JGSFFsMlFYQkZURkJlNXpickVCQ3BZZlNqS1F5YkpBbk9nK25YZmYyb1Fsc2FXNVgvRm9EUHhnaVUxRnZCSlppdkdwaXBHYkIwSWw4ZmlIQWVKcGwrMWdYc2pXV1gzclNSdGVieXJHbGoyNkcvemhQc2RKM3hkKzk1b29UQ2UvRlMramJQKzIzWEEvNjE3UUR6QmltcmlIRGNnaVVsdnYzOHVmOWI0aEQ2bG81ME1uS3kwQ0RiK3VwdkJjTmVrQWc2U0ZKWDQwbXMwZURFeGsyUWhNVFNiYkZWSGlCK1NIdmYzTG5vcXJlNEllaFNSeU1HRzhwQ3NITVJvc1NISm9QWDV6SkNZWDJKZm44UFpNZFJreGpuVHYyZjZMamJhVUVMLzJENGpNaU1tdnBxbTgxWlNaZ1VyTFhPUUxuR1lKZmZERGZsNEdQL2EyK3ZTbjhsVkZ6QVZ6a1dCWnMxSmllZktoVmJjQ3NIaGVXMU0vSlFpY3FHcFlIUWQ2Z1ZRTHc4N0hpaDMwRmNObHQra0hnaDFGdVBicTFKbHNXejJYK1psRTU1b25QSkQ4eXI0WDBaeWg3QjM2R295bm5mSnNYa05VMkFacXhYMGowSUh4cWxjNkciLCJtYWMiOiI4ZGY5ZTgyYWY3Y2VmYjk1NjI1OTU5NmZjMDk4YWMyNmQ5MWQ2OGE2NTg0ZGZiYWU5NGJkYzE3Y2Y3YzIwZDFjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InQyb3RkL2U5YVVsWlMrMCs2NWFqSWc9PSIsInZhbHVlIjoieEJVSDZPZHZsUDhtSXZ3Q0JLdCtBUTJuYW0rbUtlY0ZmcDBmdVkrZllPeHJBM1luS2s2UmcwT1VBRGhMVEx5akhta2o1blhJOVlnTW9lT0gvZVNFTFNUUVdYSGJiME5pdzR3V1MwNExwbmd4aFBxb3pYZjRKRnNkU2lVWjZUcUpYcG9QUlZNK3pVd2VUSW5jOTZQSWczdnhGKy9uWTBQcWJMdkNBOTllMmc2ZkJNVGZlamlMb1RJV1BlWnZsZGphbnNCSXBvanFtcnNDQzZ3VjY0WktHNFFzeFlPUzlpWjFRbGp6bUxLbEtOWGFqY3NQMGlhNUNxUWw3YlZlb3JEbTU0QkxLSjU1NXpRWFNKR094VDN1eHhGOEtNUXg5N3ljN1o0WFk2aVA4UEhsZWVDazUrNGZ3VkJxM0w0Vks0TStwcW9vKzBxYUZCTUZzTnJRUlBreGIvZXhTR1FwaURMRTZ0S2I2ajhZMUpXN0VwVUpXWkJCeFJvRHRmUHBYV0ZYelB1Y2V6ckx5enJRT1lvMzVwSk8wYkNxRFZyZUJRSFkwWldwQzJVWFRRSm5XSlpOcHlPdFlpbXNRVnlRbkxVa1VJbUVqWmNNaXY1V0hFcGxWTzZyZEVZVldZVngxbWJyekJGN2N4OEtsRi9HbzJzY3o3UEhwMmZYWDNOUUNILzkiLCJtYWMiOiJhY2M1ZjA5YzZlNDlkZGIxMjgyMmQ5MDdhZmFlM2E5NjRiYjM0ZTdmZGM1NzllOGM4YjA3MzY1OTk5MTRlMjdmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OxCA9rABwnsUtdJrsRyx2DLpR7CHtGXPC1CLyExX</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">h4v7KnclY0iOKyPMFu5GxMLqVTN5plmJ6SYNK0tY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1082280985 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 21 Jun 2025 01:34:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application, X-CSRF-TOKEN</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im54bkZBdzYvU2hNSkY3TXBIdlIyQnc9PSIsInZhbHVlIjoiQjlSMjVNd0RjeGdZZFN0TjJacDB3VDJJRmF3YlBaMVRxa1JjWjR1dnhVcGdZNkowUmc4VGFnK1IzL2l1S3hNaEZtbURJTkRRNllQWW9WclRJR2lFajF3dGEzbGt5MWcwRkh5VEl3d0Z5YVpwVUtmandCellPL0ZrRmN0blk3UzgyU3ViTEhQZTVobEN3ODl5VU9sOVZrNlE4K3hHenhQN0U3azEyRktyckcvOHptYUZhbnNwSGZYZmhzMXNOY1NNZTJzT3JPaDVTZ0dVeUFmMFVZL0lubkUyaXUwVXVpaFNlbFZydkp2YitEd3Y1YjhBaHl3VkRKWExseWVDNmFnR1lmS1pPYjgrTHRwZXA1My9PTEdranl3U3hMazNaSzMvNmJJQjBrTzhFWXd6VEgyRWRCcElDY3UwTWJRYk10amRIZ2RKbFRpS2JsWVU0ZXJ5NTBLanJMd1RRTlcwaXVzZ1hNR01UTHNaUkFWaWtMQnQwTm1LbFZ5VWtHR3NKc0ZZZXBtU0xoU0RvZWE3ZFhmQUVWZzdQYkFEc0dBWWc3SjRNQkpYSU9tay90ZlgwRXV6VVJzZ1N2K1VvQnVNb1NnYlJZRTVxTUtBNEF1VXVJSURPWTR3RGdlaU9ZZHROaUI4SjhZMnBnZEswbzVINjdaa1FPOW12ZGRUUUV2MFV1UXAiLCJtYWMiOiJkMWQ1ZTllOTFlYTg3MmMxMjIwZjI5YmI4Yzk3NWRjNjE1YmQwZGRkNjdiNzdmNTBhYzJjYzBjYTIwZmU2ZDlhIiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 03:34:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkJTK1dOWWk5MGFwa3pVd1MxSFAyYXc9PSIsInZhbHVlIjoianhUR2tQb2NaWkRhYTBJb2RUTkVaZTRPK3d0RjNCVjlORXRGZUdYSXBGdWZRYytWOEcvTEIwWXJwcUU0V3NjVkxOeExxQXl4aXpFZm1RRi8zWlR2WHJXRHRiWGk0d1Rtc3E3MVRXOEJlandWMWpTSXlGNEkxd21idFcxMUE4M1g1RFdwbGpPUGVwY1lmWDk2YVZpRkt2MTVHWXlUaHc4cStneENwR2VYWGlWSk1HSEVWZU00V3lKbmRCc1I1YVg2cnNxTVFIK01zQVZ6V2ZGelAyZ0NENzQxRklmdkZ2cjgyTTNDRXpqTkFXV2ZSWmRuZGwvTk0zVWw4VFVWUlR6Yk5SYnBPT1BrTGhhNjhJQy9ibll2ZTVlT0pBRm1EcFlJd3JOTG1iQWh2WnZYQjcxRVhpUTc5aGRGQTBNWHphQ2JQVEdXUm0xNnV6Vm1EVDk0MitPYXVXOWtQTno2V3FJQ0RiTFJhUUZPZHJSeUI2bkJSWXd3ZU1HUHd1aHZsSFMwaXYrY2ZFcWZDZmNXMlRPYzJ1UDlWZjZLaUtNNGRDbjVsREZjcUsyMXllekd2WGsxaU1qcGwxZ2JuL0FrV0FZTFcrbXZWVWF1TGN6aE5HTWNjSlNWMmpGNUthSmtMcWdtdXN1NmtQV2pJblNzYVZyajZNbmpsS3VMSVk2eFErT1oiLCJtYWMiOiI2NDU1NDM2MGZkMDYyZWM3OTE5N2NmYzg4YTFiZTM4MzhmODU5NjlkOWM5YjQ4NzdiOGU5M2FlOGIyY2U2NzQ1IiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 03:34:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im54bkZBdzYvU2hNSkY3TXBIdlIyQnc9PSIsInZhbHVlIjoiQjlSMjVNd0RjeGdZZFN0TjJacDB3VDJJRmF3YlBaMVRxa1JjWjR1dnhVcGdZNkowUmc4VGFnK1IzL2l1S3hNaEZtbURJTkRRNllQWW9WclRJR2lFajF3dGEzbGt5MWcwRkh5VEl3d0Z5YVpwVUtmandCellPL0ZrRmN0blk3UzgyU3ViTEhQZTVobEN3ODl5VU9sOVZrNlE4K3hHenhQN0U3azEyRktyckcvOHptYUZhbnNwSGZYZmhzMXNOY1NNZTJzT3JPaDVTZ0dVeUFmMFVZL0lubkUyaXUwVXVpaFNlbFZydkp2YitEd3Y1YjhBaHl3VkRKWExseWVDNmFnR1lmS1pPYjgrTHRwZXA1My9PTEdranl3U3hMazNaSzMvNmJJQjBrTzhFWXd6VEgyRWRCcElDY3UwTWJRYk10amRIZ2RKbFRpS2JsWVU0ZXJ5NTBLanJMd1RRTlcwaXVzZ1hNR01UTHNaUkFWaWtMQnQwTm1LbFZ5VWtHR3NKc0ZZZXBtU0xoU0RvZWE3ZFhmQUVWZzdQYkFEc0dBWWc3SjRNQkpYSU9tay90ZlgwRXV6VVJzZ1N2K1VvQnVNb1NnYlJZRTVxTUtBNEF1VXVJSURPWTR3RGdlaU9ZZHROaUI4SjhZMnBnZEswbzVINjdaa1FPOW12ZGRUUUV2MFV1UXAiLCJtYWMiOiJkMWQ1ZTllOTFlYTg3MmMxMjIwZjI5YmI4Yzk3NWRjNjE1YmQwZGRkNjdiNzdmNTBhYzJjYzBjYTIwZmU2ZDlhIiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 03:34:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkJTK1dOWWk5MGFwa3pVd1MxSFAyYXc9PSIsInZhbHVlIjoianhUR2tQb2NaWkRhYTBJb2RUTkVaZTRPK3d0RjNCVjlORXRGZUdYSXBGdWZRYytWOEcvTEIwWXJwcUU0V3NjVkxOeExxQXl4aXpFZm1RRi8zWlR2WHJXRHRiWGk0d1Rtc3E3MVRXOEJlandWMWpTSXlGNEkxd21idFcxMUE4M1g1RFdwbGpPUGVwY1lmWDk2YVZpRkt2MTVHWXlUaHc4cStneENwR2VYWGlWSk1HSEVWZU00V3lKbmRCc1I1YVg2cnNxTVFIK01zQVZ6V2ZGelAyZ0NENzQxRklmdkZ2cjgyTTNDRXpqTkFXV2ZSWmRuZGwvTk0zVWw4VFVWUlR6Yk5SYnBPT1BrTGhhNjhJQy9ibll2ZTVlT0pBRm1EcFlJd3JOTG1iQWh2WnZYQjcxRVhpUTc5aGRGQTBNWHphQ2JQVEdXUm0xNnV6Vm1EVDk0MitPYXVXOWtQTno2V3FJQ0RiTFJhUUZPZHJSeUI2bkJSWXd3ZU1HUHd1aHZsSFMwaXYrY2ZFcWZDZmNXMlRPYzJ1UDlWZjZLaUtNNGRDbjVsREZjcUsyMXllekd2WGsxaU1qcGwxZ2JuL0FrV0FZTFcrbXZWVWF1TGN6aE5HTWNjSlNWMmpGNUthSmtMcWdtdXN1NmtQV2pJblNzYVZyajZNbmpsS3VMSVk2eFErT1oiLCJtYWMiOiI2NDU1NDM2MGZkMDYyZWM3OTE5N2NmYzg4YTFiZTM4MzhmODU5NjlkOWM5YjQ4NzdiOGU5M2FlOGIyY2U2NzQ1IiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 03:34:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1082280985\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1140439809 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OxCA9rABwnsUtdJrsRyx2DLpR7CHtGXPC1CLyExX</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1140439809\", {\"maxDepth\":0})</script>\n"}}