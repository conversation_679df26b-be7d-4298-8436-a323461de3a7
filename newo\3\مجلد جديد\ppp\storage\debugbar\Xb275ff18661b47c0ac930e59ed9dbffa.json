{"__meta": {"id": "Xb275ff18661b47c0ac930e59ed9dbffa", "datetime": "2025-06-21 01:34:52", "utime": **********.347968, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750469690.987921, "end": **********.348009, "duration": 1.3600881099700928, "duration_str": "1.36s", "measures": [{"label": "Booting", "start": 1750469690.987921, "relative_start": 0, "end": **********.143924, "relative_end": **********.143924, "duration": 1.1560029983520508, "duration_str": "1.16s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.143979, "relative_start": 1.1560580730438232, "end": **********.348014, "relative_end": 5.0067901611328125e-06, "duration": 0.20403504371643066, "duration_str": "204ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46148200, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.01332, "accumulated_duration_str": "13.32ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.231416, "duration": 0.00866, "duration_str": "8.66ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 65.015}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.2711132, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 65.015, "width_percent": 11.261}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.305698, "duration": 0.0016, "duration_str": "1.6ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 76.276, "width_percent": 12.012}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.323064, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 88.288, "width_percent": 11.712}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "OxCA9rABwnsUtdJrsRyx2DLpR7CHtGXPC1CLyExX", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-635927742 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-635927742\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1114390457 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1114390457\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1522703791 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OxCA9rABwnsUtdJrsRyx2DLpR7CHtGXPC1CLyExX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1522703791\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1659519266 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwy%7C0%7C1960; _clsk=1updagk%7C1750469669053%7C3%7C1%7Cj.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ild6UGR5MjdPTDBIZjc1R0FoaFlNSlE9PSIsInZhbHVlIjoiN1hIbmUxeC9XYVJtMk1yd2VXNlZPTStWWW81SFkySkVHa01hZ2h0Z1pId1ZPZFBSRjBVc3RhcUhTRms3bXVhbTcyNnBMWXdaN2FsZzJNWE05Ri81Yk1IRjhkN0ZVTVIwZmJxN2NiNW9vTDhiWkVIRDlJTkdrc0hVNjlPUjR6RUtMTnhBdlZQbVB4bHY5MkJjRUpyQm0rOUZQak5aRGVuM3RCRlBTSjBLSzBuV2hMWVR1eW82ZlRoWWZqaEczSzZWbzV6S0tXaXlXejQwTkVtejJNTDRtQUF2bTVlWTZYNDl1YUdxSkI1am1wVzI2QzFZeVJPRnFLdElHZlVXaHlQY1pKT3RUQ1NLaENJYXlSaE9aa1h5SENXMk1HdUgwYmE5bWNOazZHRFZUeU93cktTQm8yZnhRbmtEcEw0cTgyUkYzb0lzVjVkZGNHckxCYm40UGpiTm5aWG9reVBMQWtJNVMxS0FzMzMyVE1senlqWkVobVkwYU9ZRXZsTnlBR0hqTm4yUnhjSGczazJOWXg0ZmpWNEk2SGVsRkJ5VTZSU3hVSU91RHkweVFGM01TUktocXhyL0NTN3gvVFhUY1htYjcrTGlwbmhJQXltYnhXYVMwQmczZEwvRTJIUjFqbXYvRy8xckI0QXRXanBaM2hiWkFiTjZBaFJoRFFPWHFBYUoiLCJtYWMiOiI1OTAwODdhY2E4ZjJlYTM4Y2Q4NWU3NjczMTFmYmRlYWY5Yzc4OWI0OWQyNTIyZWIxYWFlOWJkNDAyZTg1OTMzIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkdMSVJSNHU1RUVDRjIvcjZxWGJoaGc9PSIsInZhbHVlIjoiRW1vaUFNVEVrbnFVSEZYcEhjS1daZGZNWXFyQ3RwWFRjbm9qdTRaT1RUSndsUnlmbGNCcllZWHJBSUhKbk5RTHRYS2FiajYxa2g2WlF5NWRtcUxXV0dIYVBjWkFmb3JXdVFpVlIxc2V2ZzBuWFE0V3RSRjlhMDBwZlhOMjZyUEhuMlVoMm1iMHpEZ1AxVk95RmkzZEVDOU8wSFBGc1BNSzdabm05aGxGTU9LN1ZuVWNKQ1I2R0w3Q3FKbzkrSys5azNMVVJUeE9Yd2VydzNhV2RWa1F3QUp2TUJ5TXhZOS9kRkZRamhZRXVOdzRVWW01WDJCbm00RW1PeCtCYTQxM0lvVUhPZFNXMTl5N0dCZUNZMTFQNmdFUDZwSGlpcmliTEh1a1JoNVI2SXcyellYay9xQ1ZpYkhDcm5scFEvRFZBQWN5TjhXYXJjSjRzenB3aXZ1amxZbGxoWFFqUTN0WS9jT1d1N2FZbmFLcWdTL3NjMnM4SzZBNExRN2pWMHZMT2lCSTY3YU1LdkZMYmY3NEJLbHk1ZVY2R0xRYjBySk9Ob3NQbUh3K2dJUjdXZHhjdEU5Tmt1OXVMMFRRM0o0OEJOVGs1WWRoeFlXTUprR2lQQkhvUkJjYThuUm5PaUNqM0VBa1ZFdUlLbXlxTVFzZWo1bVp5aUFZTkpFMnQzbFciLCJtYWMiOiJlMzQ5N2VmMzJiMTQ2YTg2ZDExZGExZGNjNzk2ODBiMjY0NDNiMGNjZTdlYjNhZDY5YjZhNTk4OTM3MmQ2Njg3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1659519266\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1156531977 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OxCA9rABwnsUtdJrsRyx2DLpR7CHtGXPC1CLyExX</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">h4v7KnclY0iOKyPMFu5GxMLqVTN5plmJ6SYNK0tY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1156531977\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1774963732 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 21 Jun 2025 01:34:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik5xWi9OS2xtbjZsUmltNUpaQktrYUE9PSIsInZhbHVlIjoiZ3RpajhvakV0eEIvRzEybnpvRGtMc3hlZmVXNjB2NW4yVDRqc0tra0p5Q3RjWCtkOG9zMytQWTN1STd6YU94a09ramNEaEQ3OXJPNFFhWWlQQzF2ZWM3Y0YvaC9PZExTMmUycFJVbnVtTG1vQnJJMFR2UE9SYWsxRk5mWnNMWEgzWHdZNHNjZnF3eEhXLzhSb2xJenBsOVgxNnlUWGJiK0xzc2x5T2JTYkUwNHdzcmQyTEVNVVBETmtnT1JwSEZhZWdXSFMxR21RTTE0ay96bGlLZTNJR0M5MEVNYnVYRzhjZEUxYUt2MVVBMmN1dGU1U3oxK2dQYXlPRHEyL2VHc0dSTDJFVmpZcTZ6Y0V5NXBDWVhJM0hac0NSb0p1M1VjS2NGNzdSazFkcVc4bk45MWRpQjc3NTEycyszZ3pPQmtiUzVGSU5WTGNGbTAwcWUzUmM1YVpVaWdvUHN3SnpnTWd4akNWdWF6RENsWEYyOVBRZ3U4WTJobWpMZlgwSkZ5R1FiKzNSR0pQNkJTVGU1WVVVR0E1Zm8ra0tlTkxVdzUzUWlPR0ZlSHR1azJBWHdwRnBVNDZUeTJoY0hjVEp1V1IzN3J4S01QTmVMcDhrUmtoZElabFU4VERUa1hKMTRnSFRCT3FVY1cxQU5VV3RTaGx3TSsvODF1cC9KQjlzRTAiLCJtYWMiOiI2ZjI2NDM3MTcwNGNlZmNlOTE5NWEzMDliYjdiZDBhMzk2MjVlOTZhOTUxYWZlOThkZWRmZGU4MDM1ODFjMjEwIiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 03:34:52 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkZVSkUyVUc1UWtOaUplSWl6WmxLbFE9PSIsInZhbHVlIjoiSUhhZ3FJYWxrSzhIdGZvS3hWUExlNnpldXppdXBFc0NRTWlvZkVYMDV3LzNyNDAwRzZsMVA2ZVFheEVLdS95RVgzY0g2R1duSHFoN1RzMVBJbHQyOFhobWNwZ0h3anplRnRQQjhiS0JPbHdqVndNQTBLc2JGc0J0dHlFY2hYTXhGVVZpTEpZTWRCSml6Qk5yeUp2bGwzVVdMazV2R3c1Q2FMcndFdi9jYTB1MVhnVUtITGpPbDRWRHNWekVEM2twU21lUmZUbTNwR0ppaXdhb0RxR2dIWTF5Qm4wSzRwZXJnSWw4SFRqUHFzYTZYejhka2VlTjlKZGNBQU5DQzRsQjk3Z1FNUnBkMk8rbXZqS2RTaVlSNzBpb3MzcTNTMHppWHVZc3hZTWxFYTdZVWtBV09YNFBXODRvcm9rVjVsQzVmV1dLRjhabURkR2g3cGFoOXQ3MjZqSnphbTQvQmcrTXo3VFpKTlZ3UGRpeU9QaEpDSVNEYlNNUHA0UG9iTzRWaGhmUzFGa2prblJwdTZocVZobnovUGVyUTBPRlBCTk1sVEFWUU9CTVQ5Q3plUElEaUtrcVpSSk0wL21tb0Q3ZmdCWXk5aCtGY1NrcDgwajJFYUJCN0VMVXlkYkJQRElmZldodWxGc29TU0ZoSlJ2RmZncmlkZHlUUEdoNmsycmsiLCJtYWMiOiI5YWVlM2RhOThjYzUzMjVmY2FmZmVjNzg0M2JmZDllOTgyMmRmMTY3NjYyM2JhZjIyMDU1MTJmNmUzOGQ1MzQ1IiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 03:34:52 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik5xWi9OS2xtbjZsUmltNUpaQktrYUE9PSIsInZhbHVlIjoiZ3RpajhvakV0eEIvRzEybnpvRGtMc3hlZmVXNjB2NW4yVDRqc0tra0p5Q3RjWCtkOG9zMytQWTN1STd6YU94a09ramNEaEQ3OXJPNFFhWWlQQzF2ZWM3Y0YvaC9PZExTMmUycFJVbnVtTG1vQnJJMFR2UE9SYWsxRk5mWnNMWEgzWHdZNHNjZnF3eEhXLzhSb2xJenBsOVgxNnlUWGJiK0xzc2x5T2JTYkUwNHdzcmQyTEVNVVBETmtnT1JwSEZhZWdXSFMxR21RTTE0ay96bGlLZTNJR0M5MEVNYnVYRzhjZEUxYUt2MVVBMmN1dGU1U3oxK2dQYXlPRHEyL2VHc0dSTDJFVmpZcTZ6Y0V5NXBDWVhJM0hac0NSb0p1M1VjS2NGNzdSazFkcVc4bk45MWRpQjc3NTEycyszZ3pPQmtiUzVGSU5WTGNGbTAwcWUzUmM1YVpVaWdvUHN3SnpnTWd4akNWdWF6RENsWEYyOVBRZ3U4WTJobWpMZlgwSkZ5R1FiKzNSR0pQNkJTVGU1WVVVR0E1Zm8ra0tlTkxVdzUzUWlPR0ZlSHR1azJBWHdwRnBVNDZUeTJoY0hjVEp1V1IzN3J4S01QTmVMcDhrUmtoZElabFU4VERUa1hKMTRnSFRCT3FVY1cxQU5VV3RTaGx3TSsvODF1cC9KQjlzRTAiLCJtYWMiOiI2ZjI2NDM3MTcwNGNlZmNlOTE5NWEzMDliYjdiZDBhMzk2MjVlOTZhOTUxYWZlOThkZWRmZGU4MDM1ODFjMjEwIiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 03:34:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkZVSkUyVUc1UWtOaUplSWl6WmxLbFE9PSIsInZhbHVlIjoiSUhhZ3FJYWxrSzhIdGZvS3hWUExlNnpldXppdXBFc0NRTWlvZkVYMDV3LzNyNDAwRzZsMVA2ZVFheEVLdS95RVgzY0g2R1duSHFoN1RzMVBJbHQyOFhobWNwZ0h3anplRnRQQjhiS0JPbHdqVndNQTBLc2JGc0J0dHlFY2hYTXhGVVZpTEpZTWRCSml6Qk5yeUp2bGwzVVdMazV2R3c1Q2FMcndFdi9jYTB1MVhnVUtITGpPbDRWRHNWekVEM2twU21lUmZUbTNwR0ppaXdhb0RxR2dIWTF5Qm4wSzRwZXJnSWw4SFRqUHFzYTZYejhka2VlTjlKZGNBQU5DQzRsQjk3Z1FNUnBkMk8rbXZqS2RTaVlSNzBpb3MzcTNTMHppWHVZc3hZTWxFYTdZVWtBV09YNFBXODRvcm9rVjVsQzVmV1dLRjhabURkR2g3cGFoOXQ3MjZqSnphbTQvQmcrTXo3VFpKTlZ3UGRpeU9QaEpDSVNEYlNNUHA0UG9iTzRWaGhmUzFGa2prblJwdTZocVZobnovUGVyUTBPRlBCTk1sVEFWUU9CTVQ5Q3plUElEaUtrcVpSSk0wL21tb0Q3ZmdCWXk5aCtGY1NrcDgwajJFYUJCN0VMVXlkYkJQRElmZldodWxGc29TU0ZoSlJ2RmZncmlkZHlUUEdoNmsycmsiLCJtYWMiOiI5YWVlM2RhOThjYzUzMjVmY2FmZmVjNzg0M2JmZDllOTgyMmRmMTY3NjYyM2JhZjIyMDU1MTJmNmUzOGQ1MzQ1IiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 03:34:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1774963732\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1363770731 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OxCA9rABwnsUtdJrsRyx2DLpR7CHtGXPC1CLyExX</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1363770731\", {\"maxDepth\":0})</script>\n"}}