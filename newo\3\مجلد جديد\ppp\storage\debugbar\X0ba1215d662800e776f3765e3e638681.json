{"__meta": {"id": "X0ba1215d662800e776f3765e3e638681", "datetime": "2025-06-21 01:34:48", "utime": **********.488603, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.188355, "end": **********.488635, "duration": 1.****************, "duration_str": "1.3s", "measures": [{"label": "Booting", "start": **********.188355, "relative_start": 0, "end": **********.267949, "relative_end": **********.267949, "duration": 1.****************, "duration_str": "1.08s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.267971, "relative_start": 1.****************, "end": **********.488638, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "221ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02767, "accumulated_duration_str": "27.67ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3550642, "duration": 0.01466, "duration_str": "14.66ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 52.982}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.39822, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 52.982, "width_percent": 6.035}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.454875, "duration": 0.01134, "duration_str": "11.34ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 59.017, "width_percent": 40.983}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "OxCA9rABwnsUtdJrsRyx2DLpR7CHtGXPC1CLyExX", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwy%7C0%7C1960; _clsk=1updagk%7C1750469669053%7C3%7C1%7Cj.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImhUQ3NHTVlRNnJtZVhTT0RTbnJ1UEE9PSIsInZhbHVlIjoiL09MSkpYajc1TVpjYW1KVEN4WFNFd2Z5NHhkZGMzVUR4dzdqNnJUZmxSSk42L3lpczhWOS9UZERpRitmbEM4SnZHVTJELzJoc2k5LzZhdUhCcmVrVTdJNzBGeUpHVkovQ2psZTc4c2cyaVdmbVZ2c3F5VkhCSnhrbFliN2dydWxIS3Z2N29sazh3V3dCNmdCZlRXSGFya3hoTU80YmVpaTNZWVloWEdrVjU1Tkl1Q3ZUc2xYWUxkRzJpNndHY3BKSUpid0VmbkZvYVI1b2l4MVZTVkRydk9GTk5pMU9VbUtGNTcxUHB3T0hKdEtQcVJiWWh1TVJoZnlLLzkyRnZHT1lVcjI5L2tsOFZMZklsU25ZbGVVWDFLdngvUVk1dmFKc1NBMG1BNjZiL2hKZzkzN2JmMXZFa1FFWVV4MDcyK0pzeENtZmZYNENDS05Wb3duOEp6MTBZK2RYK3FPQ1dRVWU2cWxDK09uR0llYjJtN2pGU0NrYjc0RmVTWms5d0dkWXAvN2xaVUNzV2RRSEt6YXdnaTVlS01MYm40dFM0a0NQR0JRTWdhTzFNQXJ3bEpGZWMzTGhMNjIyUTR2NXFkaStEYXpBWjNDZXpxTlZudURsTVlXWndNYi94dUNUWGRRWHRiT2U5ME1ZSEU4Y0JyQ2U1anJaZlFnUXBwYVAyRTEiLCJtYWMiOiJkM2RiMDk4NWIyNTYwYjE5NzU0OGY1NDk5MDVlZWJlOGI0ZDVjZTE4NGVjODJiOTdjNDQ0NmUxN2JhOTNkYjdjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlNhZkVQMGVUTEJrMTMxbEM2WXZOdWc9PSIsInZhbHVlIjoiL1gxSEtub0NNd2F1MUY5aTNvd1hxaEFPVzZMSzlFMDNXVktHbVY1OGRoT1J1dVZSRDdkcWtZcTA2VGc4S3BIUFhjTTV3T0x1cmgzbVQ2SFd4VmF6VUVUTmlHSkVHdzV2SmFrVmlELysrRkZZTVFlTGkxZVpDdlNWZGFWNngwUUkveUlLelZ2alI1aHQvcjJYUHk5VUlZZm1Ebms0TEF0cXBUNXhGNDczNG9OOGxHQUwvb0VEVzFvdit5ZjZWVXcxRHVlWU9jRGlxKzJxZjBGQWQzNEZqNzM4TG1hT1U4UXorVG9OT1h1emxXcUZRWE9RbWVkRTJwTjZzbkFjMms0ckJ6U3ZkdVNKWVQ4d0x5YW9CalFSSWpaS0hjUGRTcXBLWllCTk80SWRFQlhpSCtJMUI2Q2oxbWx3NTBPanlEdVp3eHlNNDBkZnFhQ0k1eVF4WXdUZFU5T0x0ZWhSbkNUQlJ0K3pxcWFudDhiNEhreDhtQmpNektUMlBUTVBqdGgxVXEweVhwUFRUd2J4U3k0Ry83aXpsSk9vdkwxQVhnVXYySzBEVWUrYWJYY2RsSWpnYVRxVXQrN0M0NlA2dVFzdGFsSTVnWWlKdWM2UGd3eDFoU1ZxbElPaWx5VEFKaXZoRTc5OTBSeWJwT01uSFVrMjlUNmhac24rVU1SL2RtbG4iLCJtYWMiOiI4ZGE5OTE2OGE3OTQxOWRjMTJhYTdiYjQxM2MwY2M3NTNhMzcxZDJkZTQ1MTUyOTBlZTcxN2IxYzZhNTcxYWI2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1906012350 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OxCA9rABwnsUtdJrsRyx2DLpR7CHtGXPC1CLyExX</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">h4v7KnclY0iOKyPMFu5GxMLqVTN5plmJ6SYNK0tY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1906012350\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 21 Jun 2025 01:34:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InJ6ZW04aHBIdzV6a1l2a2JCcHJuR0E9PSIsInZhbHVlIjoiSS91M1ROeWorVGd0UW5YMnBtMkZsZEd2cXVrVzFPd25uK0RZMk5oMVdhWjR6QXJCOXk5ckM5Z0tSYXZnSXRpejZnR2FDQncyZEhiMEhlVFlndHNhYTZ3cGk4NHhxZ1QrQ1Q5cEk5d0pmREw1NDRIa3Q2dlUxekVXeU9ESEl2cnNKdUFQWG5Wa1NOZTZjOGhybFgxMlJjTzVGTlVSaVZmRGpjQXFvakVFRUFZTzJoRnlrNUx6bFlLTjh4MzZWL09RYjNzZmtCR285enJqUjNIcDcrd1NJaCtLMXZtV3dRVlhlMVo1a0IrdlNrOFRVbHZyVVB3K3A4TXZLMFVMZTFxbkcyUWpUVmhFSElmcWt0SUF6czJUVUpieGtscmtCa2lMY1ZHVFgyNVd5SFU0L0tiMmRlU2g3VlJpZHNoNEhnc0JlOG1DNlhXditwaFhyREpVcjg4b0UyRm5XdWRNRHJmcTdnVVBqays4aklpMmJIVzBmNEROUHBuQkFRR1hqZ0RmMlZ1azA1M0lNa0xiNVM2dCtLNWFLSkhiNk9rMWcvbXljb3VlT0tpS1NTY3hxRWhhVXBHanR6WVFLTmp1SzV1c3dEKzNueXBkT3Ezb0hIUU0zYjh2MENTNWQ5Zmtja3U4ZUNDY3NqckxQeDVPN3NDbWxOdEFraDJRMExmM2lRNmUiLCJtYWMiOiJiYjBlZDNkMTYzMTQyOGQxNjgzMTJlYTYxNWQ0N2NhNzVlMWEyNGFmNzhjZDMxMjg4OGZlYzUwM2Y0YzA5MTg0IiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 03:34:48 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Imx3a3dLdlFFSHBwTEFFdjVvdXdFUlE9PSIsInZhbHVlIjoibldnWWJia3dva1BYZG9McE1CN2svYWJWQ3p0Vi90dGF6RFMwaTJNTnhpTVVDZ2RJUU96MUdSbStTYXFmYXNDMTdQY09kSDN3bGFCUTNGdnA1b3BZcjY3M3RjTTZkLzFBMjR5K3VxYitPanE4SXo0MmpJbGtpelcwM1l5cHFPV2xxbW55MnlaZ1g2NDBKcVFNRXpXL3pIRmloTFlRNzBjQ2FSRE1Rb3JKWTQ3UG16VmI0enY3REJQRUJrZmtic1RWUDhPZGpjdGdicUkrVUcvcGxTei8zMXN6NlZzd1ZJMm9BaE8wOTdaOG15cXc3MUpQSDVnYzJVWGVYeGJTOWxvSkN4SHR3ZjMrcmF1NlJCTm03VFZJZkFyeXE0SEZrREQ2Zk42MWJkaEwrOEljdTV3TXB6YjM2RElOT2Y5b0p1QlVBK3ZVQUs2ZWZiL3Q2ZzZxUitOT0ZLM1dIVW1VemJwakVWVWIrYUp6Q1ZkSGtyRjZtVFpPQnc3SFhaeXIvSXNPdG5wVUw5UlUxWCtQbEoyS2ozbU1nOW9mTUQ0RmorYjZGMDI2VlNFUTB1MWFveXIwTXpVYjkyYVpUdDdoMGtYcWJiYkh0U3ZSK09raFZmc0NHSWk3V0luZXV3Zm5hT3BnNGU2QktyaW1pMVM4cExQcy9vOWFXdExTVUpId25lSC8iLCJtYWMiOiI5YjIyZTlhYjU1N2QwNGQ0YjgzZTBiODVhNjIyMTBiNDdkMWQ2MDcwNDIwYzg0NWU2M2MyMWY4ZDZkMDc2MWY5IiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 03:34:48 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InJ6ZW04aHBIdzV6a1l2a2JCcHJuR0E9PSIsInZhbHVlIjoiSS91M1ROeWorVGd0UW5YMnBtMkZsZEd2cXVrVzFPd25uK0RZMk5oMVdhWjR6QXJCOXk5ckM5Z0tSYXZnSXRpejZnR2FDQncyZEhiMEhlVFlndHNhYTZ3cGk4NHhxZ1QrQ1Q5cEk5d0pmREw1NDRIa3Q2dlUxekVXeU9ESEl2cnNKdUFQWG5Wa1NOZTZjOGhybFgxMlJjTzVGTlVSaVZmRGpjQXFvakVFRUFZTzJoRnlrNUx6bFlLTjh4MzZWL09RYjNzZmtCR285enJqUjNIcDcrd1NJaCtLMXZtV3dRVlhlMVo1a0IrdlNrOFRVbHZyVVB3K3A4TXZLMFVMZTFxbkcyUWpUVmhFSElmcWt0SUF6czJUVUpieGtscmtCa2lMY1ZHVFgyNVd5SFU0L0tiMmRlU2g3VlJpZHNoNEhnc0JlOG1DNlhXditwaFhyREpVcjg4b0UyRm5XdWRNRHJmcTdnVVBqays4aklpMmJIVzBmNEROUHBuQkFRR1hqZ0RmMlZ1azA1M0lNa0xiNVM2dCtLNWFLSkhiNk9rMWcvbXljb3VlT0tpS1NTY3hxRWhhVXBHanR6WVFLTmp1SzV1c3dEKzNueXBkT3Ezb0hIUU0zYjh2MENTNWQ5Zmtja3U4ZUNDY3NqckxQeDVPN3NDbWxOdEFraDJRMExmM2lRNmUiLCJtYWMiOiJiYjBlZDNkMTYzMTQyOGQxNjgzMTJlYTYxNWQ0N2NhNzVlMWEyNGFmNzhjZDMxMjg4OGZlYzUwM2Y0YzA5MTg0IiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 03:34:48 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Imx3a3dLdlFFSHBwTEFFdjVvdXdFUlE9PSIsInZhbHVlIjoibldnWWJia3dva1BYZG9McE1CN2svYWJWQ3p0Vi90dGF6RFMwaTJNTnhpTVVDZ2RJUU96MUdSbStTYXFmYXNDMTdQY09kSDN3bGFCUTNGdnA1b3BZcjY3M3RjTTZkLzFBMjR5K3VxYitPanE4SXo0MmpJbGtpelcwM1l5cHFPV2xxbW55MnlaZ1g2NDBKcVFNRXpXL3pIRmloTFlRNzBjQ2FSRE1Rb3JKWTQ3UG16VmI0enY3REJQRUJrZmtic1RWUDhPZGpjdGdicUkrVUcvcGxTei8zMXN6NlZzd1ZJMm9BaE8wOTdaOG15cXc3MUpQSDVnYzJVWGVYeGJTOWxvSkN4SHR3ZjMrcmF1NlJCTm03VFZJZkFyeXE0SEZrREQ2Zk42MWJkaEwrOEljdTV3TXB6YjM2RElOT2Y5b0p1QlVBK3ZVQUs2ZWZiL3Q2ZzZxUitOT0ZLM1dIVW1VemJwakVWVWIrYUp6Q1ZkSGtyRjZtVFpPQnc3SFhaeXIvSXNPdG5wVUw5UlUxWCtQbEoyS2ozbU1nOW9mTUQ0RmorYjZGMDI2VlNFUTB1MWFveXIwTXpVYjkyYVpUdDdoMGtYcWJiYkh0U3ZSK09raFZmc0NHSWk3V0luZXV3Zm5hT3BnNGU2QktyaW1pMVM4cExQcy9vOWFXdExTVUpId25lSC8iLCJtYWMiOiI5YjIyZTlhYjU1N2QwNGQ0YjgzZTBiODVhNjIyMTBiNDdkMWQ2MDcwNDIwYzg0NWU2M2MyMWY4ZDZkMDc2MWY5IiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 03:34:48 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OxCA9rABwnsUtdJrsRyx2DLpR7CHtGXPC1CLyExX</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}