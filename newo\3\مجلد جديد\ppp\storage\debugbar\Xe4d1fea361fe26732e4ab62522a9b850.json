{"__meta": {"id": "Xe4d1fea361fe26732e4ab62522a9b850", "datetime": "2025-06-21 01:35:34", "utime": **********.099719, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.809076, "end": **********.099755, "duration": 1.****************, "duration_str": "1.29s", "measures": [{"label": "Booting", "start": **********.809076, "relative_start": 0, "end": **********.906411, "relative_end": **********.906411, "duration": 1.***************, "duration_str": "1.1s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.906438, "relative_start": 1.****************, "end": **********.09976, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "193ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01892, "accumulated_duration_str": "18.92ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.007474, "duration": 0.01579, "duration_str": "15.79ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 83.457}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.048524, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 83.457, "width_percent": 5.973}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.077951, "duration": 0.002, "duration_str": "2ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 89.429, "width_percent": 10.571}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "OxCA9rABwnsUtdJrsRyx2DLpR7CHtGXPC1CLyExX", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwy%7C0%7C1960; _clsk=1updagk%7C1750469731893%7C7%7C1%7Cj.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlpTeDc4cVdJVmdEODNza2FXcXVRN2c9PSIsInZhbHVlIjoiSy9QYTRrT0svcFpsUTE1SkFGNFhjbEtHS2NWWG5ydWljeUt2T0Rsa2UwZUxVbW9rSHRWOWZLVElqS0ZTSm9ZVVp3a0o2bFlaZU5ISjdPN0x6WnBWRm15ZWF2b2RQZndFdXhBd0h6bE8xQWhEZHpuMWZvL09XbklpWkgvcEtGNFpTUml2aGU3MS9XZnd6cWQrcEpRRzRJOUNhaUx3bnoxWmJEYWIxN0VTQ1lxaWVld2Y2V3dGNlZINzZQaklGY0dOTFhIdUFPdDZ6cEYwd3NGZ21wMkxmY2VKakh3TnJkZXUzWVdzLzNVV2F4QitiejBJR3grdFVFN29MZ2oyRXc4eGgwdklKM0MxeE82U1hMR2cyVVppdzViUklWMTBzZU1aaVRyZU8wQjdZeDE3cU1QNXovdkpRQ3NZN2tXMFFqeXFiTjlCQ1Y2Rk92QmtOdmZBT3ZhcmI2YmlMUmxPN2ltVk9UUC9MMWpheXZ6RmNGSldhOUZOZCs3V294bmxLTUdYSmNaMlFudlJYczRsTkdTdC9tYmxENnJ1Nm1BTEdDOXZROVBxOTRyWExGQzVyV3hmcWd6c3RramtLTncwdFd0c3pSSGF3V3BqemRHN2FtUW9weXA1YURmOWVWb1V5N0s5N29uRXByVER3TmZCUVhRMzVWbWcvRXMwZkR1VENoRWMiLCJtYWMiOiJlZDM4MGYwMTI3MzA0MGZmMjE2ODBjZTEzNGNmNWI3ODc1N2YxNDI2ZTkzNDRlYzIyZGJhMDdjMWZjMjgyNTc5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlVpOGE0Nks2NkptWkd2MGlOaU1kRWc9PSIsInZhbHVlIjoiaE1rNXd2ZGpCT3BSeTJxSEZGZS9CczlnWTNZK1Rhcmk0V2RNTDcyMXlvUndseXBlU1d0cTJaUnBTWEtyS0JoQjcyaDc3SzhobEU2Zm5oL3pxYmQzK1FnaXJpLzBJWXh0VWlJV1d0Z1dQZHM2cWdsVzFrbkZ6Z3ZkSGNOTFl4Yi9JR2kxNlhDeEt4RUlsVmF0TTcwaG5pM3NTY08rWTF0c3VEZ3lKY1V5bVVHVnpnRFJmaFNvQ0g2V2JDQXJVS3pTdjlYdGtpSHBwbFNtMFNjcFp2am05OEZ1a1FIR05tbkIyaGZ0a1dBK1lVYmtTODFSQ1U2ek1CUFQyVHUwR2ZNMXhld0d5YytrVm9YL3F2cWY5b05IWVUzZzJTblBvSWZjSmdjNi9xYi9IdkNRT0pyV2NSVGJ4ZTg1dStHalhFa21WQVBrN1hTMTY0WDhka2NINE51M1VwY203WEUxUkV4Z3g4UjhESEZja0hOUkc3bDV1QVE0a3dXZDJBYUJ2WTNIaVRpOEdza2xqNkZablF5R21aaW9OL3dsbHVackNRNHR6NjlqV042alJFNzFiVkRnTERDWkpWcjhUVGRMS1dSdlFGOVIzUXoybS9hQkwrMWUwOU5aeVl6WnBGLzFGQXBQVWhuU2pKc1F4aWdsYjg0TzF4SlRUSjJRdXRiVXhCb0IiLCJtYWMiOiJjMDljZjA5YzAwNDlhYmE5ZjUxNDFiMDVjMTdlYTVlNDg3ZjJhZGMwODBjYTc0YzFkMWE3YzE1NTI2MzEyNGVjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1121788616 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OxCA9rABwnsUtdJrsRyx2DLpR7CHtGXPC1CLyExX</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">h4v7KnclY0iOKyPMFu5GxMLqVTN5plmJ6SYNK0tY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1121788616\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-426246892 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 21 Jun 2025 01:35:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application, X-CSRF-TOKEN</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ilo2V3RVQjBMQzZ4VlNSRUtkUy9kTHc9PSIsInZhbHVlIjoiWTNHQVQ1M1JBNm53aFNRL1RtcUJEMUswa0hmNUwrT25OZ3g1b2Q5TlBWdHE3MUEra3RnK1JyaEM4dzJMbXFobGI5V0U5NWZLd2VPUDhVOFU2ZlhQdm5WeTA5ckdWdE5BRExDVWIwZ3VyMTN5U0F5SlMxM0I5WVRNNjVTa2dPbVlkZTV2UWFaTFFlZTUyeTI3clJKVW91K2tGdWpCSm41c2JwY3cydXRWUkZqcmdPOHR5UEszVlc0UHh5c2dOcjJSUy9BOGQwUmJyNWZ6MVFSNFVnUkhnaDczZEM2bjg3NjRiYktWUHlyenYyYW91Tmp6eG9xVzVxM3U4TXhlQWU2am81ckdiVVNWMzJHSmRPUUdKN2R0RDA1ZzdHZldYYkdmMDMrS2hEQlBSTThncFQ1T3NwNSs4ZWRNNjhyNU02QUdJbDJEZzkzUGEyLzRYeTR1R2RUY2NXdzB1Z0hYWHZaRnRSRVNuR3pGZXQyaVlCYXBiREN0TWlqTVZyZDVyMTR3NjJKQXBqUkJxMGVrWERxdU9Cc0w3cjZtSzlmRGhxSEhuU0JoYkFQd081Q0xlbm50YkptbGplSVA0b0dEUUhmNWNiWUlBV0hQRitGRXZmOUhxSWVZT3NQUFgrVkhCdXRlaDhlb2kxWFNVK1BRRGYySWNKdzRkOGRBWkV1S1d6VlkiLCJtYWMiOiI1NjFiOTNhMzVlYzllNzg4ZDM5NTNmZTlkMzVkYjhjZDU0OTQ1ODJmMzAwYmFjMWYxYThjOGI4ZDFlZGEwNTExIiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 03:35:34 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImtjUEIwT056eXJqdGlXU1FVRjl1K1E9PSIsInZhbHVlIjoiWm8yWGFmdkV1Q2I1c1VhLzZjVVVOV3AxT210ZEl2UlBOZlJWbXc3WVBoNWk4eFl3aExHWm9hZlZMYVJhdDhXNm5qeElsUXA5eERrYnViQTVJY1RyNGpXLzdVNitEdGg2emViN0hTV0JObUZERGFSa29EeWVXWlhXRi9UVzViY3lIWThyNm5BMmdKbisxdEFNUjQvSGtVTEVTNHN3eUNqTnpkTHIycmJ6MFdESjIrcy8xZmlaWHJ0N0dWU0hLOXVWYVM5SklFZXhycEdWQmZ3bU5MektpeEovdkZUck5tbWFVWGV4Tzc4OTBDeDU4azM4V1J3Mmt0MkZiMUI3RStRZHZDMXdDcktjekJKSEhJcTFERGZUbkw2cDJVZGVpa0NxN3Flb3J1YXdUUFBFOS9uMk81K3hDNG1WbXFRZm5MMTdsRnl1cnVPbTZTV3YvQzhUdHduZlJXSmRsOWhKZ0NlKzZZbXdZZUg5V0l4L1BxNnZOOFZpUTh3bGpkdVlpVzlkc1BsMUxHOHZmZVM1cE90NXVjM0lyNmZNeGdweXVyQ0d3QWtKblFiMkN5YzhNMElxYSt2WnBLSXpaRnZlYi9vYnhQMVlJU2I2b2w2aGRjTFpmUkRJZWJTSjk1RlRMZjVpMFl5Q2VCNFA2RHA2bVhnRmJIekFSNE5DOHZCMjFadXkiLCJtYWMiOiIyN2E5N2VhMzFmNmYxZDI3OTQ0MTFkNDI3ODkxNDg1YTczOGY5MzAxNDFiZWM3OTVkMTBiMDBlZDRiOTgzYmEzIiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 03:35:34 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ilo2V3RVQjBMQzZ4VlNSRUtkUy9kTHc9PSIsInZhbHVlIjoiWTNHQVQ1M1JBNm53aFNRL1RtcUJEMUswa0hmNUwrT25OZ3g1b2Q5TlBWdHE3MUEra3RnK1JyaEM4dzJMbXFobGI5V0U5NWZLd2VPUDhVOFU2ZlhQdm5WeTA5ckdWdE5BRExDVWIwZ3VyMTN5U0F5SlMxM0I5WVRNNjVTa2dPbVlkZTV2UWFaTFFlZTUyeTI3clJKVW91K2tGdWpCSm41c2JwY3cydXRWUkZqcmdPOHR5UEszVlc0UHh5c2dOcjJSUy9BOGQwUmJyNWZ6MVFSNFVnUkhnaDczZEM2bjg3NjRiYktWUHlyenYyYW91Tmp6eG9xVzVxM3U4TXhlQWU2am81ckdiVVNWMzJHSmRPUUdKN2R0RDA1ZzdHZldYYkdmMDMrS2hEQlBSTThncFQ1T3NwNSs4ZWRNNjhyNU02QUdJbDJEZzkzUGEyLzRYeTR1R2RUY2NXdzB1Z0hYWHZaRnRSRVNuR3pGZXQyaVlCYXBiREN0TWlqTVZyZDVyMTR3NjJKQXBqUkJxMGVrWERxdU9Cc0w3cjZtSzlmRGhxSEhuU0JoYkFQd081Q0xlbm50YkptbGplSVA0b0dEUUhmNWNiWUlBV0hQRitGRXZmOUhxSWVZT3NQUFgrVkhCdXRlaDhlb2kxWFNVK1BRRGYySWNKdzRkOGRBWkV1S1d6VlkiLCJtYWMiOiI1NjFiOTNhMzVlYzllNzg4ZDM5NTNmZTlkMzVkYjhjZDU0OTQ1ODJmMzAwYmFjMWYxYThjOGI4ZDFlZGEwNTExIiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 03:35:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImtjUEIwT056eXJqdGlXU1FVRjl1K1E9PSIsInZhbHVlIjoiWm8yWGFmdkV1Q2I1c1VhLzZjVVVOV3AxT210ZEl2UlBOZlJWbXc3WVBoNWk4eFl3aExHWm9hZlZMYVJhdDhXNm5qeElsUXA5eERrYnViQTVJY1RyNGpXLzdVNitEdGg2emViN0hTV0JObUZERGFSa29EeWVXWlhXRi9UVzViY3lIWThyNm5BMmdKbisxdEFNUjQvSGtVTEVTNHN3eUNqTnpkTHIycmJ6MFdESjIrcy8xZmlaWHJ0N0dWU0hLOXVWYVM5SklFZXhycEdWQmZ3bU5MektpeEovdkZUck5tbWFVWGV4Tzc4OTBDeDU4azM4V1J3Mmt0MkZiMUI3RStRZHZDMXdDcktjekJKSEhJcTFERGZUbkw2cDJVZGVpa0NxN3Flb3J1YXdUUFBFOS9uMk81K3hDNG1WbXFRZm5MMTdsRnl1cnVPbTZTV3YvQzhUdHduZlJXSmRsOWhKZ0NlKzZZbXdZZUg5V0l4L1BxNnZOOFZpUTh3bGpkdVlpVzlkc1BsMUxHOHZmZVM1cE90NXVjM0lyNmZNeGdweXVyQ0d3QWtKblFiMkN5YzhNMElxYSt2WnBLSXpaRnZlYi9vYnhQMVlJU2I2b2w2aGRjTFpmUkRJZWJTSjk1RlRMZjVpMFl5Q2VCNFA2RHA2bVhnRmJIekFSNE5DOHZCMjFadXkiLCJtYWMiOiIyN2E5N2VhMzFmNmYxZDI3OTQ0MTFkNDI3ODkxNDg1YTczOGY5MzAxNDFiZWM3OTVkMTBiMDBlZDRiOTgzYmEzIiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 03:35:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-426246892\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-524973665 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OxCA9rABwnsUtdJrsRyx2DLpR7CHtGXPC1CLyExX</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-524973665\", {\"maxDepth\":0})</script>\n"}}