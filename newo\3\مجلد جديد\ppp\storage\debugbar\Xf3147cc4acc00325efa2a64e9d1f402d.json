{"__meta": {"id": "Xf3147cc4acc00325efa2a64e9d1f402d", "datetime": "2025-06-21 01:34:36", "utime": **********.912066, "method": "GET", "uri": "/cookie-consent?cookie%5B%5D=necessary", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750469674.795753, "end": **********.912105, "duration": 2.116352081298828, "duration_str": "2.12s", "measures": [{"label": "Booting", "start": 1750469674.795753, "relative_start": 0, "end": 1750469675.96894, "relative_end": 1750469675.96894, "duration": 1.173187017440796, "duration_str": "1.17s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1750469675.968958, "relative_start": 1.1732048988342285, "end": **********.91211, "relative_end": 5.0067901611328125e-06, "duration": 0.9431521892547607, "duration_str": "943ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52579720, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET cookie-consent", "middleware": "web", "controller": "App\\Http\\Controllers\\SystemController@CookieConsent", "namespace": null, "prefix": "", "where": [], "as": "cookie-consent", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FSystemController.php&line=2387\" onclick=\"\">app/Http/Controllers/SystemController.php:2387-2439</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.02123, "accumulated_duration_str": "21.23ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 341}, {"index": 21, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 70}], "start": **********.1180959, "duration": 0.019899999999999998, "duration_str": "19.9ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 93.735}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 71}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/SystemController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\SystemController.php", "line": 2390}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.1482031, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 93.735, "width_percent": 6.265}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "bSqGKRzn8zrN52whBJ5jpetFrYHJ06phOnPEUsYY", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/cookie-consent", "status_code": "<pre class=sf-dump id=sf-dump-806594920 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-806594920\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1554502867 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">necessary</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1554502867\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1613494084 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1613494084\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-829191348 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=h9hzj7%7C2%7Cfwy%7C0%7C1998; _clsk=1pfi5p6%7C1750469660818%7C2%7C1%7Cj.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InZFbmltSmN0czRDRkNlYTBHU24wdVE9PSIsInZhbHVlIjoieFlTWHJlSzVXdXRqTHVDbW9pMmxOV0V2a1NKdkJycGZuSG1aOEo1RVdvOXFJaUxnUm55STdOOWRLV29vOEQ3VU41SEN0OTFlbmNsUVd0b3JOamNSWnB1cGMzVE91d3I0VGZvczRscC8zVE9rRklsWkp3RkVNWWx0Y3h1NGVMbU5pRkQ0YkU1cmZyVnpwRWVsWkxzTmFGa09KRkFSQnErdUJod2cwVE9wYVZLbjBPZjByT09VYWk0ek9IVS9FQVZ4Q2lHT1JQdmVoM1V2TjVwaldUTVlGdURreFZ6bzJEdXhkSE1zMzltcngvd0p5VXdIWUsvUVArazBZUERBc0JDMXN6ZEZwcndKYmFlOEU5SzVaVjJrdlJPK0NucUZnYTVkazd1WmtYbis4dE9DRkhQMG1YYnRpTUhDeGE5elM1Wk5LWFVaK1FQZjM2UHlZT1NqeHpzNTZBY3EvV2k4d0NsTFdrUHdSZ2E3dkpBWi9mMk82eGxlZDIyb3RFeEVUbjFoblNaNjhRUVBzcEZIY2hZTFRvZzFpT0E5bHdaL1JqdEd1RjdEOGNlZmVZSzZ4eWpSVks2MUNaS3JnRGo4NFBmQi8xK3BsREt4UmZQcEp0VlIvaE0xVm9iQ1ZFQUR2TFU2TUl2Y01VOWlDeVhLSlRzWWpvbGg0TVJUNjBFZU1XZFMiLCJtYWMiOiJlZjJhMGJkY2VhNmY2YzY4OWJlNDZjYWRjMDZmZmJiYWYwNjg1MjkxMjczMGRlM2Y5ZTFmZjM2MWQ4ODhkMDk3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjBOeXNMMEVXanZ6S1llN1pGM2FrV1E9PSIsInZhbHVlIjoiSndvSDNpUHN1NG5qU2UwaFBFbmpTd1ZOUzlVbmI2OTYwdUJHc0VrUGZtZCtVSGs1d3QwM0kzVHNHTDdDYUlId3FlVkczTEZrcXNyUElEbEVncndnWjNCTk1UMFFkK1VyYnJNTlArOGtzSnpFb0dpQXhjNXNUVTg2aHVCTTd6NnZxUDBRcU9iSU5nS0tmZEpaTEVKWFhaQXVLYzZQN2Y5SUxkaGd0clI2L0IwSDNBRFNkejgzN0FMRjFjVDZpZXpCTFlRL1k3NE9OaldPY2xudkw2cm5XcnpEZU9IRnF2dHBwZHEyWElLeWlIeVlzUzhPK0Zlb3M5RkNONEluVUZDcDBRVU5YRHkxR3BwM2lRcXNYcVRhQ1ZuQ20zY3ZmWFpoSUZkcUpEN3JuNzZkbjQrU0hMQm1lTHQ3a3lmUHJabDZsY256RFpPZFd3TzJ0RE8wME1TcmNweXJhUEJ3NW9nMUU4MFE1cklvWUV3NzBWOUpsVWh6Unp5UDkwSkxBNVBKb0lPTHhQbTBsYWxYL3hkYkRaNlFUNHd4Y0RyR285dzlIUm03ekNKUzV6V0Z6bFZqLzQ0NEJYdytaRXZ1NDA4V1BtSi9wQVU1Q2dsQmxkdnVhRnQzakgvNUNtLzR3VytVSmxFYUUyV2c0N2M0ZU92RzIxUmMrQnlDc2VMMjA3RzUiLCJtYWMiOiI3MDBmY2VjYmRlOTg4YjZiMDZmZmVkZGM5YmI2YzM3MWVkMTQzNjVmYWJmZWU2YTg1M2VjYmI1YzFlNGNlOWE4IiwidGFnIjoiIn0%3D; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-829191348\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1439813424 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bSqGKRzn8zrN52whBJ5jpetFrYHJ06phOnPEUsYY</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HB3SpfsNW9qh15emZQ8PQBnZsYUESoWIqimBy8Yg</span>\"\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1439813424\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 21 Jun 2025 01:34:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlNsVlpkRzd4T2JQMjlhT2pBK2g3MGc9PSIsInZhbHVlIjoiMFAzRGJRZStacE1Zblp2MkxoYkh4V0ZVeUNURHgxTmZMMkUyaXYxWUd0RTQrWGlrODQ1OWdrNEJzdUdCWVh0NFpMYlhTY2pOVU1sT25BN2M4UUNYN29naFZJaHRaQ0ZEMThVNGt1blFhaC85ZmJoWXdyZEZYSkNLaTNzLzhEMDdad0lwS3h5d3VUZkFjVlBOaXlRRTdaZWp3WFdoK240elJ2d1lHRUtlY0p4MEF3QktraDhHODdNTkJ5OWlTVzNhNDMwem4rVnhnUnI1cFNCZit0THpqeWhJKzQvVE4zVUx2RVJhRjFxYXRkN0toejVEV3RTdCs0NnRLeS9SaEN2aTBubXVBYWZhblJJalRqcHdDUjliK2xzem8rV2NtZEUzUFAra2Y3eURVVkpqWnpwOEtjZFhGWkRZa2JxVUtnZjZrM3NXVFB1eUIyMzEzNk5Ydk95VU1LY1E4QmV1VHNwdkZhR0d4cE9HMFg2MU5aVDVNbHBMYVhyRFZYZjVYc21TTEpRSVlDakpIR0pWWXpwTk5qdTN0aTI3WTJxVlBndXN1KzdhRVJDdU1VTWYvTFVYeHpzY2NCSWQ0dWM5K1EyQ3Q1UTF1M2NMd3czM0ppbWdwbk1qR2QzL1RDZ3ZkL0VKTWV5cXdQUjMrSFc0Q2FuQzlpWXFEbHlCRWZodzdXa1kiLCJtYWMiOiIxZmY2NjFjN2I0MDNkOTZjMGNlYTQyNzdiNGViYzEwODkxY2ViZjhmMDYzZDc4YjMxZDQyYmU5MzY2Y2QyYjhmIiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 03:34:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InZSU2FQVVBLYlFsaW8xRnJXRnYxbWc9PSIsInZhbHVlIjoiekdtenN5MURKMnpiamhYYUN5Nk1vNm5BbHV0K1VJWTF2b0NZa2VOM1RpZThPTGNJZGcxa1RXL3ZiQzRDM29WSlNNTkNYb2xDUHIyYTRRVjh2OFFCZmRqdVdLL3BZZ3NmNGlJK2pzRGdRdGJHdnFROTRHZ0d3Q0t3VTJlQlFna0xic0JYbEFVUjhNc05ZVGNBZVJPRUxKVlR1ek1ZQzdUUzErNXEwdTU1eWxoYVRRN0hzUmNBNGxhVHBaa3lOVUgvRkZ2Y0hPb3pvUmsvcUd4WjFpWWJaaEluS1NIN0ZsUnpGRldHSmlFWDFUckVrQXEycTVTUFpjMkpiWXZURUFiMUQ1TDFEcTVuZmVZSGpSVVVKbFZ5cnIrV21MeDZlYkxaNzkxQ3ZkdFQ1OFFTUkJ6TG9vWmp3MGUzTkR5RmN3WWcyN0s4MmJPMG5COHBhbEZBaHVwRG9oOCswSCsvZ1dRK0t0cFNjVnZaQUhBc1ordGdNeXBNZzFiR2FVSkY3MWZudFdkUjVSdkJtWXdKNkxxdTN3bnFxWGhpbXJiaFpOSCszYlBaUmZ3QzVibWEvbk5zeVVlbG9XS2hGREZ2R2d3RzRrcndjNjRCcEJ3TTM4ODFwRWwvQmw4ZC9ISmpLVHJGSzZZMHZiOWFMQUlVTkxBWkhTalhxT25HMWZudVVzemgiLCJtYWMiOiIyMGVkZjBiOTYyNGFlNjRlMjFiOWVkMjBkY2NlYTNhMGRhNjUxMTQyNGFiNjdhMTIwMGY1ZjQxOTM4MmRjYzQyIiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 03:34:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlNsVlpkRzd4T2JQMjlhT2pBK2g3MGc9PSIsInZhbHVlIjoiMFAzRGJRZStacE1Zblp2MkxoYkh4V0ZVeUNURHgxTmZMMkUyaXYxWUd0RTQrWGlrODQ1OWdrNEJzdUdCWVh0NFpMYlhTY2pOVU1sT25BN2M4UUNYN29naFZJaHRaQ0ZEMThVNGt1blFhaC85ZmJoWXdyZEZYSkNLaTNzLzhEMDdad0lwS3h5d3VUZkFjVlBOaXlRRTdaZWp3WFdoK240elJ2d1lHRUtlY0p4MEF3QktraDhHODdNTkJ5OWlTVzNhNDMwem4rVnhnUnI1cFNCZit0THpqeWhJKzQvVE4zVUx2RVJhRjFxYXRkN0toejVEV3RTdCs0NnRLeS9SaEN2aTBubXVBYWZhblJJalRqcHdDUjliK2xzem8rV2NtZEUzUFAra2Y3eURVVkpqWnpwOEtjZFhGWkRZa2JxVUtnZjZrM3NXVFB1eUIyMzEzNk5Ydk95VU1LY1E4QmV1VHNwdkZhR0d4cE9HMFg2MU5aVDVNbHBMYVhyRFZYZjVYc21TTEpRSVlDakpIR0pWWXpwTk5qdTN0aTI3WTJxVlBndXN1KzdhRVJDdU1VTWYvTFVYeHpzY2NCSWQ0dWM5K1EyQ3Q1UTF1M2NMd3czM0ppbWdwbk1qR2QzL1RDZ3ZkL0VKTWV5cXdQUjMrSFc0Q2FuQzlpWXFEbHlCRWZodzdXa1kiLCJtYWMiOiIxZmY2NjFjN2I0MDNkOTZjMGNlYTQyNzdiNGViYzEwODkxY2ViZjhmMDYzZDc4YjMxZDQyYmU5MzY2Y2QyYjhmIiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 03:34:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InZSU2FQVVBLYlFsaW8xRnJXRnYxbWc9PSIsInZhbHVlIjoiekdtenN5MURKMnpiamhYYUN5Nk1vNm5BbHV0K1VJWTF2b0NZa2VOM1RpZThPTGNJZGcxa1RXL3ZiQzRDM29WSlNNTkNYb2xDUHIyYTRRVjh2OFFCZmRqdVdLL3BZZ3NmNGlJK2pzRGdRdGJHdnFROTRHZ0d3Q0t3VTJlQlFna0xic0JYbEFVUjhNc05ZVGNBZVJPRUxKVlR1ek1ZQzdUUzErNXEwdTU1eWxoYVRRN0hzUmNBNGxhVHBaa3lOVUgvRkZ2Y0hPb3pvUmsvcUd4WjFpWWJaaEluS1NIN0ZsUnpGRldHSmlFWDFUckVrQXEycTVTUFpjMkpiWXZURUFiMUQ1TDFEcTVuZmVZSGpSVVVKbFZ5cnIrV21MeDZlYkxaNzkxQ3ZkdFQ1OFFTUkJ6TG9vWmp3MGUzTkR5RmN3WWcyN0s4MmJPMG5COHBhbEZBaHVwRG9oOCswSCsvZ1dRK0t0cFNjVnZaQUhBc1ordGdNeXBNZzFiR2FVSkY3MWZudFdkUjVSdkJtWXdKNkxxdTN3bnFxWGhpbXJiaFpOSCszYlBaUmZ3QzVibWEvbk5zeVVlbG9XS2hGREZ2R2d3RzRrcndjNjRCcEJ3TTM4ODFwRWwvQmw4ZC9ISmpLVHJGSzZZMHZiOWFMQUlVTkxBWkhTalhxT25HMWZudVVzemgiLCJtYWMiOiIyMGVkZjBiOTYyNGFlNjRlMjFiOWVkMjBkY2NlYTNhMGRhNjUxMTQyNGFiNjdhMTIwMGY1ZjQxOTM4MmRjYzQyIiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 03:34:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-66212086 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bSqGKRzn8zrN52whBJ5jpetFrYHJ06phOnPEUsYY</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-66212086\", {\"maxDepth\":0})</script>\n"}}