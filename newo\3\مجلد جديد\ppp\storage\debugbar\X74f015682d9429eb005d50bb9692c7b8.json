{"__meta": {"id": "X74f015682d9429eb005d50bb9692c7b8", "datetime": "2025-06-21 01:35:26", "utime": **********.196386, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750469724.858823, "end": **********.19643, "duration": 1.3376069068908691, "duration_str": "1.34s", "measures": [{"label": "Booting", "start": 1750469724.858823, "relative_start": 0, "end": **********.001549, "relative_end": **********.001549, "duration": 1.142725944519043, "duration_str": "1.14s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.001572, "relative_start": 1.1427488327026367, "end": **********.196435, "relative_end": 5.0067901611328125e-06, "duration": 0.19486308097839355, "duration_str": "195ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46165200, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.029759999999999998, "accumulated_duration_str": "29.76ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.077, "duration": 0.02532, "duration_str": "25.32ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 85.081}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.1329682, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 85.081, "width_percent": 3.898}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.15904, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 88.978, "width_percent": 4.906}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.1751418, "duration": 0.00182, "duration_str": "1.82ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 93.884, "width_percent": 6.116}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "OxCA9rABwnsUtdJrsRyx2DLpR7CHtGXPC1CLyExX", "_previous": "array:1 [\n  \"url\" => \"http://localhost/forms\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-27382074 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-27382074\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-140782565 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-140782565\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1261966680 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OxCA9rABwnsUtdJrsRyx2DLpR7CHtGXPC1CLyExX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1261966680\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1569777501 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/forms</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwy%7C0%7C1960; _clsk=1updagk%7C1750469723287%7C5%7C1%7Cj.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkZxTXpHLy82R1prTTE1bTB0blA2UFE9PSIsInZhbHVlIjoiNThRZ3FOOTVCK0dFbzBXRVhQWlNiRTdZOE9JVGVlN2VUSzVGQVp6TTdvZkVIYXFVc09MU2xrYlBKN2tNZEhWdm9sVG1VcDE0N3pUS2g4T0FLeUc0MExZdm8ycmFaK3BkWlNteHFrM2tzRmR4N1lnQjBHTFFET24yV1M1NzVWNjlzVEdqbTFpUkxtYVA5akxSVUl0UXBRRS9KSW01ZG5JcEtPT3NlODJOWE1CckZEQVU1V3BjTlQ3VEZjSTlXc2daWDNaMTFOVmR0bmtaZFJNWVZ0aWw0YnY5QjE4R1BUY0c4cjhibUVqaUNDM1Ivb2lMaHAxaUtUMmJzVS9iZ3hLUXBrTGl6dzJHcW5VWnQ5ZkdWeFVHMzZWd1FxcXRxVVdWWVRZSVZuK3dUZ2lOamtpa1o0TVc4Z1JGc3c1WW5IMEpHNEN2aUNubmFLancvcko4Tm1QRGJLTnJTK2hQbm56QW9wek1EMFYvWWI3SnExMDFHQ3RuL2J2VXJHWjh3UjN6UStlNlJ4ZnU0OEgwaEJDY1ZXWUp6YUpybUtHYnNsb0pZK3RsS2MvRWVuMUxxVWJyTkhVRVpDaS9ZK2RZYy9CMTZzYzVyUXJvVUdQK0VxMHkwcWMvb2hRNm1mSnU3TXRDejExcDc5d0dzbnhFTXFXdk9ZeEt3ZkV6dk1jRDh5VFEiLCJtYWMiOiJjOWEyNzNiNjUxY2YyYjVhZDBjMTE1Y2RkZGEwNWQ4NmJhYWJjZWRhMDIyNjJhOTU4ZjY4YTBjMzFlMDk3NjI5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkFMMngyQXNna0JYSVZPTGR0cmpJMXc9PSIsInZhbHVlIjoiQ2JTbWtlZklQdmVoVWFqS1p2ckgvTkZmdW9pUnVlZ2xlQ1JNNFhzTXEyUldJN3J2eFljeWxIcHdGSDJGaTFBV1FwcS9ybXFjdk9xOVZqd0d1R0lmTU1vTXZodVRkS0Y0Z0pZYWMySEhtNnZiWVVpVU8wNjV2a3RSbTdBVzhSMDZ0V09EYUhlZ0VkUjQ5d3dzRVBxcnd4KzNmanZlbkp6RmQ4NUdnYkdGcE1OZy9Wa3VoZmRIRWZtb1BFbnAvSDhRZ3RsVm5BcnRQTWZJSWpHYzhEYVVpT01lenJ4YjNVVzZEYUNxbFU0YWpCS1I2YzBRbmlucTd4MXI2MHZwcnY1ZDdVbVQ3Z0sranRjSHU0UndST3gvYzExWFZoby9qSjU0U2FBWi95Q0l6VUlBR2ZUNmExTVJQVGc3UVdsbktZL29ZdDhXQkN1aTFZNDNwc0tQSkxTcXloSlRBQ2ZVei92ZENORUY0SFhOTDNFZDIxenViVlc4UTE0T2U2bEtSOG5KMERhOGwrZzNUQjN2TVZoMngrV1JObFphcDIyc0NsZitRMkdvRytIQ0dHZk5TUkR0aTUvSkExTXZva0xEeVlBZ1F4LzZKRnFGbk1tQTBFQ1VvM0wwdkFYNVFUTGVQSnpUbWZCT05wQlRTQ1VVdkQ0SnpXYytCdEhrclJ0NHRlNG0iLCJtYWMiOiIwYmZjMzFmNWEyMTlkN2QwODQ1NTJmNGE4YTY4NGViNzAzMDU2ZDA0MzE4ZDQwZmUxZGExZTEwOWUwOGQ1ZjJjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1569777501\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1176744349 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OxCA9rABwnsUtdJrsRyx2DLpR7CHtGXPC1CLyExX</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">h4v7KnclY0iOKyPMFu5GxMLqVTN5plmJ6SYNK0tY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1176744349\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-357086864 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 21 Jun 2025 01:35:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IktOZUJWSXVFV09kdWR6THVzM3VUMmc9PSIsInZhbHVlIjoiemxTTldZaGJ0OExybUp3bzBzT1VPZnplRHlrRS9xZytOSEZGMnJxL0lWakMvR2RidEJ2R2xCOFl3em9xc3Z6TW9sVDh3TFk1TUxJeXdSeFpDVzR6SUhXeE10M0RxSTIvZEZPcWE1S2p6bThpOXBJUTlMcitKUENITWxOYzlldGFWUEVmRjdPZmNJSDdOYXdOTjFEQ2J2MVFHUDg1M0Fzd2hYeWhSSWp6SHloZ1pUR1M1Um81ZkhvYk5Bbkw3K2N3amhJTk5saVM3YkRSODhUcklFZkxJYTBSTWk5ek1DOEFHc3V2aktSNTA4cWZ6T3VGWGUzem8xOFpjNURyeFMxUGZFUDJ4VWNSdlRqeVAxcjJJUVgxRlNpMTdZYTFOdHlpQldVb01iSGxqL3Azakh1QUEyRkNORFQ0eENSTXYxMERQMHNIblY5TCt3b3ovSGwwbXhaTng4MmdNSDZvV2ZiOGVmbkpMVDQrYUpmeXNweUtab3JMMGtVNjMzQmNkbjdQcGdxTlROUVVMZjRUdUIvWHFlQTVCWEYvUkRQdHBybC9MOUZBbVpVd3R3ZVZQMmRwcnc3OVFUTnRsby9EMlF3dUlpN1V3VXpkMmlKbzNmQlV2OGpkV1RTazlSdFQ1RkdNMUo1aDMvU2hMLytzNnpOQ3RYMEMwemJFWFZlVHlYK28iLCJtYWMiOiIxYmQxMjQzMmY5ZGIwYjVjMjY5YjQ2ZDNmZjIyYjIzZWUxYmJhOTdmMzM1MjE4YzVmYzA3MjMwY2Y0YTE3NmEwIiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 03:35:26 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InhWaDBsRG1JRFFTT2ZBOHpvd0VGOUE9PSIsInZhbHVlIjoiSWNPTFpyMTUvbGxWaDJ4elNEa1NwTm5mSVU4R3NKQlVUNHJoMGRka3FSbkpMRVNaeTdDWndCT2tiZUF0eC9KcGxzWmswdWxNek9TMjZNbk9NU0xvOXdKNXk5S2M5NDZ3emIwUHZwOVcxQkVHN1N0ck5QSU9YbHgvUEQ1U2FVWTUwMlp3UkNiN1VPV3ZkK1V2dFNSb1paSGFLbGdFZThEU3M0YmNKN2tDSFFid1dLYzlkY0NjTFRxY2FFYU8vMVFKWVNZWnpNVEhWajFiMFNBTi9Zd095S1hjZzZ5ZmV2MmNKenN6UGFaR2pucFBoN1FvNFAwdnNMYkRCeG9QVVg5am1RR054T04wbWJMMDVBZDREZzZiUFhuWkhzQ3lNSWRUVXEvUHF6L1p2VGltTDlmSWNwT3c3YUhzZ2pDeGZBQm4rRG5aM1VnZjNZcytzVWxha1JtNld6dkdSVmZZM3hZWGNWbjNJUUduTWtheDB5R0VBcm0rdnNQTDZnTjVDTnlVbElHK3ZqWW5IOFozMWpQaUdWeDJJcjV0VzBtdUZtSDd6dnNIT3JhMjhYZEJCeU1FMlRVd3FvMWFROUxBMVk3Vm90dlRyN1JFMmNzbnhsWmc2R3MrN2w5UFBoeXNiNWl1TTBmcU44bHlIT29FenBTUXZ1QWRuMUJET2dSUHBoU1UiLCJtYWMiOiJlN2Q0YzhjNTJiODJhZjVhM2MxOWM1NDUzODM0ZDk0YjJhNDMxOTVmZjAzNmZkOWU2Y2EzNjA4M2U4MGJkODAyIiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 03:35:26 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IktOZUJWSXVFV09kdWR6THVzM3VUMmc9PSIsInZhbHVlIjoiemxTTldZaGJ0OExybUp3bzBzT1VPZnplRHlrRS9xZytOSEZGMnJxL0lWakMvR2RidEJ2R2xCOFl3em9xc3Z6TW9sVDh3TFk1TUxJeXdSeFpDVzR6SUhXeE10M0RxSTIvZEZPcWE1S2p6bThpOXBJUTlMcitKUENITWxOYzlldGFWUEVmRjdPZmNJSDdOYXdOTjFEQ2J2MVFHUDg1M0Fzd2hYeWhSSWp6SHloZ1pUR1M1Um81ZkhvYk5Bbkw3K2N3amhJTk5saVM3YkRSODhUcklFZkxJYTBSTWk5ek1DOEFHc3V2aktSNTA4cWZ6T3VGWGUzem8xOFpjNURyeFMxUGZFUDJ4VWNSdlRqeVAxcjJJUVgxRlNpMTdZYTFOdHlpQldVb01iSGxqL3Azakh1QUEyRkNORFQ0eENSTXYxMERQMHNIblY5TCt3b3ovSGwwbXhaTng4MmdNSDZvV2ZiOGVmbkpMVDQrYUpmeXNweUtab3JMMGtVNjMzQmNkbjdQcGdxTlROUVVMZjRUdUIvWHFlQTVCWEYvUkRQdHBybC9MOUZBbVpVd3R3ZVZQMmRwcnc3OVFUTnRsby9EMlF3dUlpN1V3VXpkMmlKbzNmQlV2OGpkV1RTazlSdFQ1RkdNMUo1aDMvU2hMLytzNnpOQ3RYMEMwemJFWFZlVHlYK28iLCJtYWMiOiIxYmQxMjQzMmY5ZGIwYjVjMjY5YjQ2ZDNmZjIyYjIzZWUxYmJhOTdmMzM1MjE4YzVmYzA3MjMwY2Y0YTE3NmEwIiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 03:35:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InhWaDBsRG1JRFFTT2ZBOHpvd0VGOUE9PSIsInZhbHVlIjoiSWNPTFpyMTUvbGxWaDJ4elNEa1NwTm5mSVU4R3NKQlVUNHJoMGRka3FSbkpMRVNaeTdDWndCT2tiZUF0eC9KcGxzWmswdWxNek9TMjZNbk9NU0xvOXdKNXk5S2M5NDZ3emIwUHZwOVcxQkVHN1N0ck5QSU9YbHgvUEQ1U2FVWTUwMlp3UkNiN1VPV3ZkK1V2dFNSb1paSGFLbGdFZThEU3M0YmNKN2tDSFFid1dLYzlkY0NjTFRxY2FFYU8vMVFKWVNZWnpNVEhWajFiMFNBTi9Zd095S1hjZzZ5ZmV2MmNKenN6UGFaR2pucFBoN1FvNFAwdnNMYkRCeG9QVVg5am1RR054T04wbWJMMDVBZDREZzZiUFhuWkhzQ3lNSWRUVXEvUHF6L1p2VGltTDlmSWNwT3c3YUhzZ2pDeGZBQm4rRG5aM1VnZjNZcytzVWxha1JtNld6dkdSVmZZM3hZWGNWbjNJUUduTWtheDB5R0VBcm0rdnNQTDZnTjVDTnlVbElHK3ZqWW5IOFozMWpQaUdWeDJJcjV0VzBtdUZtSDd6dnNIT3JhMjhYZEJCeU1FMlRVd3FvMWFROUxBMVk3Vm90dlRyN1JFMmNzbnhsWmc2R3MrN2w5UFBoeXNiNWl1TTBmcU44bHlIT29FenBTUXZ1QWRuMUJET2dSUHBoU1UiLCJtYWMiOiJlN2Q0YzhjNTJiODJhZjVhM2MxOWM1NDUzODM0ZDk0YjJhNDMxOTVmZjAzNmZkOWU2Y2EzNjA4M2U4MGJkODAyIiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 03:35:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-357086864\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OxCA9rABwnsUtdJrsRyx2DLpR7CHtGXPC1CLyExX</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/forms</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}