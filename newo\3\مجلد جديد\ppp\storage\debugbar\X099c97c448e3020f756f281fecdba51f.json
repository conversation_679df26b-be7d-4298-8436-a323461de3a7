{"__meta": {"id": "X099c97c448e3020f756f281fecdba51f", "datetime": "2025-06-21 01:35:43", "utime": **********.147463, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.916487, "end": **********.147494, "duration": 1.****************, "duration_str": "1.23s", "measures": [{"label": "Booting", "start": **********.916487, "relative_start": 0, "end": **********.991877, "relative_end": **********.991877, "duration": 1.***************, "duration_str": "1.08s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.991898, "relative_start": 1.***************, "end": **********.147497, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "156ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01031, "accumulated_duration_str": "10.31ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.070031, "duration": 0.00692, "duration_str": "6.92ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 67.119}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.100905, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 67.119, "width_percent": 15.228}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.127444, "duration": 0.00182, "duration_str": "1.82ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 82.347, "width_percent": 17.653}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "OxCA9rABwnsUtdJrsRyx2DLpR7CHtGXPC1CLyExX", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwy%7C0%7C1960; _clsk=1updagk%7C**********656%7C8%7C1%7Cj.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IktVZ1JCL3hEa0tHcU5UT09vbzdqRlE9PSIsInZhbHVlIjoienJDS2hCV0ZtR016VFNGRDdZNHhCQ0Evcm0zL0FYSmh2RXNuclB0ekw2UFFIakZ2Unc4MTljSDRXRzJaOUZqUWpDMEtjZVA0d0lXSlJmc1IwYTh1WUozNWJiVUZoYTErUkNRVEVZVGd2S1RiY0E4U01ma09JZHRqcGR1c2wybk9MVFdzNnZNb2l3akVrVitUdzNMaWJxKzZ0T001Y05VNG1uTyt0ZE5oQTJ3aDJGaHVrZUVKMnZieEVHQSs1Y2xJZW1NWFlYNFc5dFdDYnljb1dlZ1JFdlBTUktOYUxUWC9PL1ZraEpnajEwZkY2WDBqMitNWG9yRXhwWXd1b2Rmai9hcW5rRERucDNWVWtyRGRjWjJNeHh5Sk5Tc1gvdHRQYUorR2FsSndRT1gwdUR4WFhtMzVJUXdYaTRYK0lFT1pxM20vekFDSVBsUThqV0Fua3dHR1NWdGZXdkVtSFdsMU9EVE9XK0ZmcmV1a3NEeDJ3NTR2R1RkMWtrQXY1TE9YMC8xWkx5U0hjckV3Y2lVTDV5OVJFNEV3QXJ5VlVHVnQxYzdXbGQzN3hoemdkY1kzWGpyOGV4VzcxSTkvNjJORjBpRFN3UkxldVh0NXpoMENwMndBT2drMVp0SEVXUEVUMFl1a0Z3bHJHeXJUUW85bElmRWljaE0vQXF3K05aajUiLCJtYWMiOiIwODFiZWZhYjAxNTFkNjJlODVjYjViNDYxMmExOGQzYjc1ODkyZDllYjY4MDRjNGM0YjEwYTM0N2IxOWM3MWI3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IitqUkp5YlhUaHJTWHFkaTdtQ1hWOVE9PSIsInZhbHVlIjoieXl4dmZ2WjIxcGc5aWRoWFcvRjFialVaa1BLd1VIV0FNZTBIL01lbHlJNjFrb0NYUXZwM2RaVllkQnJpQUlDVkQzUTJ0dDVZUEVaSDNVNWRtNlhUcGxGL0lIQ3VCMFRsSGVQUFNzcXJwZkIxODB3YTdLTk1mS2Nuck8rd2FJSkVabXpBdTc1c2k2NzBKOURRYzhEcnBCZ3RGd3ppdWxKWXhOMG1qYys4ajVqeldRRlEzNzQ4eVp0aDBTMHVRcGg4UWtFOWl0S09ZZVQ4WXhJRU5TaWFiUE1sa0NYa1NKUGdjQXI2UGVpcXc1OUtmMTJIQU1LRHdkZC9lTjR0TitHRFczUFFwZ2IrL211clV4eExQM2tSdHNmZE5FeWlreGhlUXZEdm82M01FRlZpQ3ZrY2ZMVTdFdWw4TjdGWFU1Y3hPdTJEUDZIZllibXE4U1Fuc0RKQVhhNjNDRkxZdXVRNFJ1VFdKcHJBa0c5dmwyNC9kVXVGRGFmZGRVVHo1Y0FXMHdhZWg1OXNkaktoY2RISDZLeFN2WFBkNjhZN2ZuUHdUNXFidFFpZFpTRW5QSkVtaDJtT1ZhK01zcEFMTzE3ZHp2UWxsaVZ3ME5FejIyUnhhdEZOditiUUo4ZWJKN0hrNXpZU1hQK01Lc1lQWndJcjRURlBvMzNKRVJKRGZkSGQiLCJtYWMiOiIyNzA0ZjI4NTIzNmU2YzljZGY3YmJiZGUyNmMxODBkMDEwZWE3ZDUzY2VjN2I4OTkwZWNkZDhhYTZkNDZmYmIyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OxCA9rABwnsUtdJrsRyx2DLpR7CHtGXPC1CLyExX</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">h4v7KnclY0iOKyPMFu5GxMLqVTN5plmJ6SYNK0tY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-370030132 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 21 Jun 2025 01:35:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application, X-CSRF-TOKEN</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImU5T0w5U0JvM0F6a3d5dGJvVmdIUkE9PSIsInZhbHVlIjoiU0ZlcTdHYk1OU1REK1VPRnMxUDBzdWNLMm9HM1ZQT3ZUb0JwTUV1NVpuQlNmSmNwL0FkdFJ1R1hSdHlrMVhtWG9VcE8wanpxRkxYTDRCeFo5UlBIYVJ2OHk1TE1URHJmRkhaZThxT0d0eVJXVkRsTG9JUENma0ZQVlJidkpFamUybWFwU0RQUUhzQ1RobXJaWW14aytuVklyNTFJRk5XYWIvSno0ajh0REN5cFVQc3FHNlpzays1dDRGZmRZdWZBQkxHcXBnQWovU1JRMXFJd2FIU1BHaDJqdHhIOE9TWlVHWDF5RjgwODhBNHNuSW9uaFdMZWRTWnNlN2pyWWVpQk45T0VZbU9JTHpkK3dvbTBpeUZVMlJiRjhEbkc1ZHVxY3BKNFh2K3oxeXF6M3JuNFVTTmg1aVJQUkRLeVl2eFlLMnRLUUlOOFh0S0dzSjJFcC9UY1lOWkNJZEVNSFB4dDM4a3VtUmx6eURCZmZaMFFVWXd1MzJhaTU3ZkhQRkVkZFR0aDlMUm1rWC90UnVyc2NGazhOeTcxT01vTGtjdnhMK2tuZ3pMR0Zaek5ocFpLcE12RkhxQ2t1R3QxMis3bjFabEJjdTBuVTFoWmRGMjF6SzFZMy9saUFsRlhYOTduRUJZdUpyaGEreHJyV2gxWkJpcXpiR0VWVEhpWmdtc00iLCJtYWMiOiI2MmNjMDUyNDIwYTk2OWU5YjYzOGY3MmE5ZmVlOWVkYjQ1YWQ2OTU5Zjg1NDdlZTgzYWQzMzExMTkxMTgzNzViIiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 03:35:43 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ik5uNi9FT3VKS0JiL254R2orM3haMFE9PSIsInZhbHVlIjoiRG9VdnB6NFFtaENBQlNEUXFQVE1SRWQ1UzJncFhaYjNrOSszQ3poUGVBQm5YaXRCcFZTVjJZOHV4WGlpQlpWMnJTV1VIT2NEakMwcHJYL0hDMTFFRGgvMmhveUZja0o5WXFNZENPL2NWS2Uzd2RkOUkyZnc3SW96cE8wTzBJQVdKdEtOa3o0dXBNaFhWREdPUXE5Rm9NL09xRXpQTXpqdDZxb2kxVTFtQkNBcW9yc0FlTWsvMmsxRmlNUkJpOXQybSthSnpQajArUDh3WHBZQW5HUXFEMzV6YTB6RXIxSnA5WWtabkVpY0U0UHV1aWFrendnSTZkSjYveU1XZjk1cU5LdnZCcjU1TDRkbVhwSlNjdjNaanVMNDJPajNDd2hYcHZVQ3JhVkVPRmVNU1dFamQxNVVGZll0ajZXVlJzaDFHOG5paTdZOXRtdUtDTVNDQjJqeXowUktSbnJQdFBnaW1pTVVOUmpmWFFoTmN2SzcyUHJ2T3Z1RlVrdmppODdHNUMxMDF5blRqOXJZckh6RW5UUVo1N2VuVFVidmRGNXRrWk1uVnRpdGpnZWlwaFhmMGVSQXY2dVBjcGdPRXdrZlVvR2ovL0hmRWk4eWlMcXdTZml5dk1ncnRBZlJKQjB3ZE5TUkhxemJGQjEwNHlTZEtpYjkwdndqY3FlUzAvZjciLCJtYWMiOiIzMTZmMWEwYjk3MDAxZGUyMTBiMzY0MWRkNTA2OTExOGI5MjcxZTRkZTFmZTU2MDg3YjRlZWJjYjZkODlmMGJjIiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 03:35:43 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImU5T0w5U0JvM0F6a3d5dGJvVmdIUkE9PSIsInZhbHVlIjoiU0ZlcTdHYk1OU1REK1VPRnMxUDBzdWNLMm9HM1ZQT3ZUb0JwTUV1NVpuQlNmSmNwL0FkdFJ1R1hSdHlrMVhtWG9VcE8wanpxRkxYTDRCeFo5UlBIYVJ2OHk1TE1URHJmRkhaZThxT0d0eVJXVkRsTG9JUENma0ZQVlJidkpFamUybWFwU0RQUUhzQ1RobXJaWW14aytuVklyNTFJRk5XYWIvSno0ajh0REN5cFVQc3FHNlpzays1dDRGZmRZdWZBQkxHcXBnQWovU1JRMXFJd2FIU1BHaDJqdHhIOE9TWlVHWDF5RjgwODhBNHNuSW9uaFdMZWRTWnNlN2pyWWVpQk45T0VZbU9JTHpkK3dvbTBpeUZVMlJiRjhEbkc1ZHVxY3BKNFh2K3oxeXF6M3JuNFVTTmg1aVJQUkRLeVl2eFlLMnRLUUlOOFh0S0dzSjJFcC9UY1lOWkNJZEVNSFB4dDM4a3VtUmx6eURCZmZaMFFVWXd1MzJhaTU3ZkhQRkVkZFR0aDlMUm1rWC90UnVyc2NGazhOeTcxT01vTGtjdnhMK2tuZ3pMR0Zaek5ocFpLcE12RkhxQ2t1R3QxMis3bjFabEJjdTBuVTFoWmRGMjF6SzFZMy9saUFsRlhYOTduRUJZdUpyaGEreHJyV2gxWkJpcXpiR0VWVEhpWmdtc00iLCJtYWMiOiI2MmNjMDUyNDIwYTk2OWU5YjYzOGY3MmE5ZmVlOWVkYjQ1YWQ2OTU5Zjg1NDdlZTgzYWQzMzExMTkxMTgzNzViIiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 03:35:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ik5uNi9FT3VKS0JiL254R2orM3haMFE9PSIsInZhbHVlIjoiRG9VdnB6NFFtaENBQlNEUXFQVE1SRWQ1UzJncFhaYjNrOSszQ3poUGVBQm5YaXRCcFZTVjJZOHV4WGlpQlpWMnJTV1VIT2NEakMwcHJYL0hDMTFFRGgvMmhveUZja0o5WXFNZENPL2NWS2Uzd2RkOUkyZnc3SW96cE8wTzBJQVdKdEtOa3o0dXBNaFhWREdPUXE5Rm9NL09xRXpQTXpqdDZxb2kxVTFtQkNBcW9yc0FlTWsvMmsxRmlNUkJpOXQybSthSnpQajArUDh3WHBZQW5HUXFEMzV6YTB6RXIxSnA5WWtabkVpY0U0UHV1aWFrendnSTZkSjYveU1XZjk1cU5LdnZCcjU1TDRkbVhwSlNjdjNaanVMNDJPajNDd2hYcHZVQ3JhVkVPRmVNU1dFamQxNVVGZll0ajZXVlJzaDFHOG5paTdZOXRtdUtDTVNDQjJqeXowUktSbnJQdFBnaW1pTVVOUmpmWFFoTmN2SzcyUHJ2T3Z1RlVrdmppODdHNUMxMDF5blRqOXJZckh6RW5UUVo1N2VuVFVidmRGNXRrWk1uVnRpdGpnZWlwaFhmMGVSQXY2dVBjcGdPRXdrZlVvR2ovL0hmRWk4eWlMcXdTZml5dk1ncnRBZlJKQjB3ZE5TUkhxemJGQjEwNHlTZEtpYjkwdndqY3FlUzAvZjciLCJtYWMiOiIzMTZmMWEwYjk3MDAxZGUyMTBiMzY0MWRkNTA2OTExOGI5MjcxZTRkZTFmZTU2MDg3YjRlZWJjYjZkODlmMGJjIiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 03:35:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-370030132\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1412195565 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OxCA9rABwnsUtdJrsRyx2DLpR7CHtGXPC1CLyExX</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1412195565\", {\"maxDepth\":0})</script>\n"}}