{"__meta": {"id": "X199c2ffb2e1eb56d09272cf7544d32f2", "datetime": "2025-06-21 01:35:44", "utime": **********.51739, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.157155, "end": **********.517435, "duration": 1.****************, "duration_str": "1.36s", "measures": [{"label": "Booting", "start": **********.157155, "relative_start": 0, "end": **********.356727, "relative_end": **********.356727, "duration": 1.****************, "duration_str": "1.2s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.356747, "relative_start": 1.***************, "end": **********.517438, "relative_end": 2.86102294921875e-06, "duration": 0.****************, "duration_str": "161ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01321, "accumulated_duration_str": "13.21ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4330242, "duration": 0.01011, "duration_str": "10.11ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 76.533}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.468035, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 76.533, "width_percent": 9.463}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.494909, "duration": 0.00185, "duration_str": "1.85ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 85.995, "width_percent": 14.005}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "OxCA9rABwnsUtdJrsRyx2DLpR7CHtGXPC1CLyExX", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwy%7C0%7C1960; _clsk=1updagk%7C1750469741656%7C8%7C1%7Cj.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InpNejNZdVB4ZEloVGp4eldRUzRHSFE9PSIsInZhbHVlIjoiSHlCODlnTFN5cDl0eVduaDE0ZEE1Nmk4eEg5d2tIUXhSenhwK0JQeFg4M1pneXRBT0pDSjF6d1ZNVUJuMjJRcW9qZWZLOGpvTzM1bm5kOEFpVkxXR1Foc0xGVjlTZCtieE1kM09vYTFTYVN3VXZnZE5rcisrYWpCN0RGYU5XZVFuc1FzanUvekJQblE5d1ZhUjRYU2ZDR2JvR210OEpEWmRYL1plNzFUdjY1NTBqWk9IeUY3ZGgwczgzQmlCc2orNDl0cVhuWHgvU1VJSTJFTFBoYmNzNzJaQ0xLdWtxVTdhSFRuTmw4UFpKNzNXME9YWWZOanFRUGFoYkhmRlpxUGZoZDBDY0ViWHVDOHgxMWZXZW9taXRMWXQ1YTI4aTRpRHhXb0MrTmIvWHlGbmVXbHVteFY0VENFbExNZ0t4MkdJMXpueHpUVnJCUmFiZ2Jaa3lZZ0NiaWJmSU5IK1ZYVXg4enlqd1AxZXJqdlRZYnFDVHdFWWpLaExpT2FNckszWDdtb3J3RE4xQ0JWRlhuVW1Oa091eVFzbmtmL3NnTDJEQXVHb0RCK0pUbUxjUEZsM2kyZlMwSE83K0szcXhYVk1HTjlPV1B5RXl4blZNT1BFTGZDTDR0cFRDZGJtaGNSQ3EwVjZ1cWpBekhxVHhsQkNOdTlLakpMMFdIK0dQb2EiLCJtYWMiOiJkNzM3ZmI0YmM1ODA4MDYzYmU5ZmRiYjJlYWY5ZGFlMWVjODM3NjE0MjNlNGExYzZlZjEwNDQ1Y2VhMTAxMTEzIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkptcUJzQWNybkp1MTUyQjh6TVZVT2c9PSIsInZhbHVlIjoiZ2ZIam95TkMvbk5RR041SDVuV3ZTTDdwSzVRTjRtRzNKOGthOGR2cXdPdEpNMm9EYjJ4QjBnenFXeE5xV1dJcGtnRld3U1RYcnZPTnpvUW5jc25tbzQzREErUmpnRnZ0TnJWVlB5ZzIrcVZXdkdrRUg5UStGVEQweTBEV0lUMXhiNkxjRy9jRG1Mc05wSjRhZGF0YVcwTzRlYjNQWGlwTWQvS1VvY1ZDczh1VjEycWd4bXJ1SzBjTWNiajZGejR4UW9SVFpkbnN1OGRqaHdMSnl5cWQyeU5DbXVMN1JESzg2MkFHYlRVMkdidS9MYWxBMzVWUmpWdERORUpYb0g5dWhaSE9XeFFnaGM3U2NUN3VJd1J0SXVPZ2pEdTFpRWJVYVhXSTNwUmk0ckV6bWUwalVNQ3R5RWEzVHVFMXZtYVRRL3RnWW40K1pxZ1N3YXlCdnduN2xGQ3kvTjBMSVhVbmg3WExiTHpucXlXaEJmSUtyUHlwRnB4c240bUlWWWpMU240VE55ejdiRU9UTk1XS3ZxWDdubEthdVNlQjAxT1FJZFlEZnEyRGNaK0tyK1ZnNFgwcXZXUFVYbkxYeUNHbG5TVW5YMm1UcjZHaTBiekp0blBKMlRUWGJkc2hNdGpCdENQMzJNVDIrT2krNVNRRVhzeUk2VjFvYmttb1VyOVYiLCJtYWMiOiI1ZDQ4YzgyN2U4ZmQxNTA1NjU5MmJjMTcwYzgyZGRjMzAxMTA3NjBjNWQzMDZlNWU5ZjFlNTNkY2YwNDRmZTRhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1078971216 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OxCA9rABwnsUtdJrsRyx2DLpR7CHtGXPC1CLyExX</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">h4v7KnclY0iOKyPMFu5GxMLqVTN5plmJ6SYNK0tY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1078971216\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1007183494 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 21 Jun 2025 01:35:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application, X-CSRF-TOKEN</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IllCSkpyRHEzWFNwaXhMOHFja0VJd0E9PSIsInZhbHVlIjoiMFI3OEJDM1ovU3JObkdNcnhUcFJJNFJZa0ZVbUlnSEZkMWhGSjJ0L0htZ2ZVWWtKWXhFRjNXU3ZjWkY2bkV6SHB1azBSclp6MGtMNWpTU1EvN3F1UjhYaTBZMFNFMTZYVndyaXNpNmhRWnpMTUdqTVVWZzlBcTRWclpMYndydElWdXh5R2VJeTh5MXl4VHliYlQyekpPR0U2RUxWLy9kb0l3VXFpOFk4TWxXYzhuNUE5THhFVWRQNUMwZVdVenpqZDhZM1lIWmxOcTBxeW9pZkdyM3hwK3cwN0NWdENST2RYcFdTeVlGZG4za3RFTzIxQlJkZHJxNlhENWRDWWlXaFJJVm53UGJCNWNpY0RDcVdFeitGcmhjQmZ1R09obm9XTzJYRXgwSGlNVnYxWGVrSXZlZTNyeHZHOGhrZ1ZheTBGcVFOMk9EMTBSQVZVcWErUG5wY3QxR29GdG9FNG16MDVid1NXS1hTSVpZVEhPbnFqakV0VHRqQmU1MFNUUnAyVVFHZDc1OHlRMElGZXcxMW9xNVVLdHhRWlNybTYrYjNubTZycGJjNCtpdWk0VnExTGhvNnlGVUpjZ1kxWlFVN2xUUS83RXhoMWtVREJMSHFXeXJQTjZQMHNoK25XWjlTd2hMM3k3MGlWcHQyMzJRRlhOVkgybkhrV3BCNFgzR0QiLCJtYWMiOiI1ZmUyNzdmZjdjNzk0YmVmODViZDA4YzlmOTJjNjJkNjFmZTk0ODljMGJlOWRlODEwYjhjOGUwMjYxYTA1ODVlIiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 03:35:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkFVaHM5MlNFOU1xOHZCUU9WVk9yWkE9PSIsInZhbHVlIjoiQ3dkNDZ1K2xDcVpUdURIZXg1RG9URldOTVdVMHEzSmR5UlFoWENFeEI4bk1xemxTNUZyVXpOTXVXL3EzeGY1R1FSRFRQZVoyZFpRYjk1dlpyTWY2VEtWeW1PK1dzMlRacFZTc3dDUFJweEdYYUlmR0RQd1B2N0l2UXRlVzJpQ0RhaGFYd2lpMHEzblowdzVDNGp2Q1FkS0FQL3RSc2xhOGpMaG1PSmV2dDNRcDh2dVZtY05uYU1qYmQvT2FWWEQ4S1FZUzFjMnVQYjVrY1JWMzdFL016NmFLSlQzajJZNThsYjB5LzY2bDNPNS83dEFRMk1mblU1MHVYMHdzUTFleGtxVDl0aXdHeU1kYlk3Z01zdTBIamNhYjFHK2U4RW5OK2twcURUdERJVVZXUm51c2loNk5Xb0ZtZlZ2a0QvNFJoTFJOQ0FpZzVaTU50MlArWGRsd2p1UTlkT1FqRkRVdGdqeEt3S1Z5cGs3T2F0YzBJU3NoaWRaT2hvbFJtcmJOVHNsdUZMRmRPT3p0aTdMRmhieThZV2RSamlDbkxUc3lnL2VpN3BiWU5qdnRZazl0MFFCRjNUdEVXMkN3aTkxY0lDK3JIVk9uY0pSRmNQaTVrVTFhV0s4V2ZiZVROelBEOUxkYkhicFJTWEV3M3d1bkYrSTBqeDNqd2xieXFLUG4iLCJtYWMiOiJlMWIzMTFiOTNhMTEzNjJlNDc4MmE4ZjkwMDVhNDZhODRkYmQ3NTZiNDQ4ZTY1ZDg4NzY5NTEyMDIzNmY3MGRkIiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 03:35:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IllCSkpyRHEzWFNwaXhMOHFja0VJd0E9PSIsInZhbHVlIjoiMFI3OEJDM1ovU3JObkdNcnhUcFJJNFJZa0ZVbUlnSEZkMWhGSjJ0L0htZ2ZVWWtKWXhFRjNXU3ZjWkY2bkV6SHB1azBSclp6MGtMNWpTU1EvN3F1UjhYaTBZMFNFMTZYVndyaXNpNmhRWnpMTUdqTVVWZzlBcTRWclpMYndydElWdXh5R2VJeTh5MXl4VHliYlQyekpPR0U2RUxWLy9kb0l3VXFpOFk4TWxXYzhuNUE5THhFVWRQNUMwZVdVenpqZDhZM1lIWmxOcTBxeW9pZkdyM3hwK3cwN0NWdENST2RYcFdTeVlGZG4za3RFTzIxQlJkZHJxNlhENWRDWWlXaFJJVm53UGJCNWNpY0RDcVdFeitGcmhjQmZ1R09obm9XTzJYRXgwSGlNVnYxWGVrSXZlZTNyeHZHOGhrZ1ZheTBGcVFOMk9EMTBSQVZVcWErUG5wY3QxR29GdG9FNG16MDVid1NXS1hTSVpZVEhPbnFqakV0VHRqQmU1MFNUUnAyVVFHZDc1OHlRMElGZXcxMW9xNVVLdHhRWlNybTYrYjNubTZycGJjNCtpdWk0VnExTGhvNnlGVUpjZ1kxWlFVN2xUUS83RXhoMWtVREJMSHFXeXJQTjZQMHNoK25XWjlTd2hMM3k3MGlWcHQyMzJRRlhOVkgybkhrV3BCNFgzR0QiLCJtYWMiOiI1ZmUyNzdmZjdjNzk0YmVmODViZDA4YzlmOTJjNjJkNjFmZTk0ODljMGJlOWRlODEwYjhjOGUwMjYxYTA1ODVlIiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 03:35:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkFVaHM5MlNFOU1xOHZCUU9WVk9yWkE9PSIsInZhbHVlIjoiQ3dkNDZ1K2xDcVpUdURIZXg1RG9URldOTVdVMHEzSmR5UlFoWENFeEI4bk1xemxTNUZyVXpOTXVXL3EzeGY1R1FSRFRQZVoyZFpRYjk1dlpyTWY2VEtWeW1PK1dzMlRacFZTc3dDUFJweEdYYUlmR0RQd1B2N0l2UXRlVzJpQ0RhaGFYd2lpMHEzblowdzVDNGp2Q1FkS0FQL3RSc2xhOGpMaG1PSmV2dDNRcDh2dVZtY05uYU1qYmQvT2FWWEQ4S1FZUzFjMnVQYjVrY1JWMzdFL016NmFLSlQzajJZNThsYjB5LzY2bDNPNS83dEFRMk1mblU1MHVYMHdzUTFleGtxVDl0aXdHeU1kYlk3Z01zdTBIamNhYjFHK2U4RW5OK2twcURUdERJVVZXUm51c2loNk5Xb0ZtZlZ2a0QvNFJoTFJOQ0FpZzVaTU50MlArWGRsd2p1UTlkT1FqRkRVdGdqeEt3S1Z5cGs3T2F0YzBJU3NoaWRaT2hvbFJtcmJOVHNsdUZMRmRPT3p0aTdMRmhieThZV2RSamlDbkxUc3lnL2VpN3BiWU5qdnRZazl0MFFCRjNUdEVXMkN3aTkxY0lDK3JIVk9uY0pSRmNQaTVrVTFhV0s4V2ZiZVROelBEOUxkYkhicFJTWEV3M3d1bkYrSTBqeDNqd2xieXFLUG4iLCJtYWMiOiJlMWIzMTFiOTNhMTEzNjJlNDc4MmE4ZjkwMDVhNDZhODRkYmQ3NTZiNDQ4ZTY1ZDg4NzY5NTEyMDIzNmY3MGRkIiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 03:35:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1007183494\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1216519410 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OxCA9rABwnsUtdJrsRyx2DLpR7CHtGXPC1CLyExX</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1216519410\", {\"maxDepth\":0})</script>\n"}}