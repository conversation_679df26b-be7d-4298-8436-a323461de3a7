{"__meta": {"id": "X24ff1c433b39fbaa797ad94c18626049", "datetime": "2025-06-21 01:35:35", "utime": **********.430463, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.111095, "end": **********.430501, "duration": 1.****************, "duration_str": "1.32s", "measures": [{"label": "Booting", "start": **********.111095, "relative_start": 0, "end": **********.240695, "relative_end": **********.240695, "duration": 1.****************, "duration_str": "1.13s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.24072, "relative_start": 1.****************, "end": **********.430507, "relative_end": 5.9604644775390625e-06, "duration": 0.****************, "duration_str": "190ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01951, "accumulated_duration_str": "19.51ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.325658, "duration": 0.01626, "duration_str": "16.26ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 83.342}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.366713, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 83.342, "width_percent": 6.817}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.401437, "duration": 0.0019199999999999998, "duration_str": "1.92ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 90.159, "width_percent": 9.841}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "OxCA9rABwnsUtdJrsRyx2DLpR7CHtGXPC1CLyExX", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwy%7C0%7C1960; _clsk=1updagk%7C1750469731893%7C7%7C1%7Cj.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjdkbTFHUXFZS0xYSlBWalJDbGc1Z1E9PSIsInZhbHVlIjoiZ1UrS3NPaXR6RnFVM2tCSGRsVjhSK2p6N3RGN09lQmdYYnVQNmRRSXV2VVJkZ0YwKzlzNitaU25jSkFIT3BKQjEyR3FJSk5NcTV2b2RDcXRZUVRXaERyaVFqYTBBNktIREJhTUZ0SVdEQVl6NnNKbFpmOE4yNTlpdmFLR2IxeG02OHFBcEVjQUppMFhLWFF1alhxeUNuY1dUb1ErRTdzWVU1V1pNNHpBWFlPZDVOaEtyQ1N4bFhLM3VSU00yQ1BjbDNIWlNCU2IzaUpFandVNExpTmhJT0Q5SWpCU3hwUWg4YkpVelZhZ3UzOThsUFl2TFRDOEtwU0FmRGRxQ0ZmYWErd1hrdDZCcDhub1pSVzJpUlJ0MTlDdktJSlVkd3pWeHA2S0FSR25OZzdYR2M5alUrSlNDbzI5ODM3b1RGYkg2cy9XSnRWVkNQSkt4MUU5UnZoa0g1ZmRvWHJRQ0Y1d1NKSXRNOENPWnB4aUZFdE5sbjVwMU9BOC9DS21iZ29JZlZERjlBckZ3bW94MEhNL3RJclNwSnZ1dEZObVZFTWs3TFpMOXNzTXNiRVY5Q3djZThlZ25vUzlxTEM2MkVLcmZES3Q5bkhmVjhHclpJaUxvbVVSempKc2JQUHJXVzFpaFpEenVBRmowTEdLYzNxM1V0NDg5OFQ4R0tNeFBMbDgiLCJtYWMiOiIxYjI3MTFkMzQ2ZjM4ZjM4MWYzNzM1OWU5NTFlYjBmMTYyOWYwMzNmMjM1Njk2OGQ3Y2ZlMzFhOWI5N2UxYzFjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Im9NNUt0SkZZYmRtZzVTTllsSE9mVWc9PSIsInZhbHVlIjoiN3JBbWg5WldHWUNZSkpBdHh3R01XSGplVVRnQ0xoYVM1WEE1bzhOUGdNRzdJeWJ2RUk1cWZxaEI4ak1zTnFPNUxrV3huWUVEZGxQWmF6eHB6a0xJQ21nc214UXpFa1RQVitVU1ZxZVlRcHJocWJuYWFnajkxSHdsZHJqelYramJETDNpUnVYRmNwZk5NUzFCV2M5WkxWNWtGM1F0ZGNzN3dyVWNMR1BQZ0VDSDZweUdWNzhPYjhLL1FTN2h1ejlHbHpIbXZIR28vRWFGZVlpYW0yUDA1dU1wVHUwYkczVWg5Q0JiK2pocjQwUFMxRWRSZWRFV2htQ1RjSWdpOFJubHgyWHM4aDh4MENuc3lQR05MZWN2TUx1WTZmbjZrTDFiMXlLelp2a0tXNWdJNjFkMEVMNHYycWViR2pjVmhYVlhrQzduVVplOXNQMzNqVFJTNnVkSnhRVmV4RVpKWk5wb0ZmckxPcS9WcDRiQjdyb0F6SUZjeUtJQjRScGNqbHFaSVl2Q3dzclhtMzhDeUJkcGgxYnFWS2FWY3dQUkdNdkN6QzRLQVFIQWtmVko5bmw1VUV6WERSRkJkS0U5dmtMK25zb1ZUb3dkdlNpZG8vOHpsZW9yY1U2ZjBSRGhqM0JyVWdKT2ZXWjE4cXQxaFd6T3g0RWx4ZS9CcFZNczVFL2YiLCJtYWMiOiJjNTNmNjlkMjllZGUyZjNkZTRmZWIzY2Y5YTc1ODAxOGJhNjg5ZmJkNDVhYTEyMTRkOGRhZjE5MTZiNDk3NDkzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-124167504 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OxCA9rABwnsUtdJrsRyx2DLpR7CHtGXPC1CLyExX</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">h4v7KnclY0iOKyPMFu5GxMLqVTN5plmJ6SYNK0tY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-124167504\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2068733699 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 21 Jun 2025 01:35:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application, X-CSRF-TOKEN</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImhkcllmSERENVQ2T1h3VzJmZ2lQNnc9PSIsInZhbHVlIjoianhnUWZTejJ5d2tSdFF1VWJPdTNEYVdmODdUNnlORGdHT29qYU5HNS9uMG1rMzY1Ylc4VW1FRUVSWFFDYW0xWHJMUjM1bkNycnpOdFlncWVuMDc3eWxqR3FQZXRaUXpsZjdrUjMrSk9mbGNaSXd0cktjQlliQ0ZuRlpVT2txK2ZTcmdiMmZqN0xOcHNCY3h6bXdBL1dmNCtORzRrQVprR3dPRHV0SGpVS1NxYlVNSXhVYWVTaXBEbzVpeU4vT1k4aWVHTndyYVFKYWNKOEJ5ZkVNa1JjWVE1Qm5SNjdJd3RjRVRlaTlOZFFiSUxRbk1oUE43NnhGU3ZvY3JubVM2T2UrT1VuV0ZQWWh1QlV4MGdDUktQeTVoNmdnR21VRWUrNWlMeDM2UGpFRzAzSnUzVUQvdjI2RHhmdWJFalRmaVVkYmNnTHJLeGlMdlVqR2M4V1VFSFB5UlcxYUVrV0t4WkFURThoRDBHejBzNWNIVlBlVHQ5NXVTZmlsbkJ0eTBOclVrMkRYOEdoU0F1MFlaRE9PeDdWNGk5QkZ4YW45WjNsUlpOODZFd1d0M2tGTXY2SmZlMklFRVFvSFBjVllrWit0KzRMemp2YVYyNllzNkIzK0c0QnY4aHJvT3JMU0VrVzRoZ2hQOUFTWG1PM0lkdEdjTFBxSXhmU0ZzNUc2S3MiLCJtYWMiOiJlYWIyMjU4N2NmODNkNGU1ZGIxZjJmMTFhYjBlYWU2MTc1YmY1MjVlMWY1YzFmNGVhNmRlMDljNmY5ZjNkNWJkIiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 03:35:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkRrZTRhV01hN1dPY0h0YnVoRjdpRnc9PSIsInZhbHVlIjoialpCZEl4eVZGNWdlTnErYmpDU2VNL25MVGFvMzJNdFl4UWd5YjNWWnNuZWZiVDVNb1VVV2tIVnlYY1JWdmtHTy93R083YzNDNWJiYkIySEE3dkxheU9iQmhwZXcrZGIxMmlQMjFrOHpSZkVBN2tONFUyV0Q3cXBFZjNoeGJ4SldZRjN0dWRDVHIrN2tmbkIvLzdEbXpvYnBxRDNEdWV0dkk4b1ZSRWtKL3RBTUtFZ2h1SktRczFsTFFnM2N5YTVvZmJNUTNzby9VMzd0d0M1UkdXeUt5T1NkNmhkdXoweEpGNWxsR3RkNzFqeS9YNWZ4WTJYdWY3RHZqeVNDdGdXbHVFSW5QaFFENTBkeElIZnVCc1krbklCa090NW5kOXNRS09qWHJIOXhYYU0xWk10REJoSEtFMTFBMHdELzI1L2J6THFjSnZKZWF4VkNQb242TTVGZ2xyR1NTUWh4V3ZQV0NpL1FBNk1md24xTWRvRVVTNHZMeWprZ0p1SmpzTUVDSjVqd2ZBTXZQRzhjOTArY0FmN1pMQ0dQMjZIWUFtSXpoalpXYXhNZWk3NDJoVFQ3STRsSDhBSVhiaXoxN2tvM1pZLzFzbmY2RmdMeWtuTkgycjVBVkRTc081TXJuNGJpVnFwbEgxcFV1V1JnaisyUnNzcEFUMjIwUDdRWVhqZzUiLCJtYWMiOiI1ZjNlYWFlMGEyYmRhZmRmYjc3ODc1YzcwZjhhYzcwYmU4YzcxODdkOTMxZGE5MTZiZDRkYWQ4NTg4OTY2NWJmIiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 03:35:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImhkcllmSERENVQ2T1h3VzJmZ2lQNnc9PSIsInZhbHVlIjoianhnUWZTejJ5d2tSdFF1VWJPdTNEYVdmODdUNnlORGdHT29qYU5HNS9uMG1rMzY1Ylc4VW1FRUVSWFFDYW0xWHJMUjM1bkNycnpOdFlncWVuMDc3eWxqR3FQZXRaUXpsZjdrUjMrSk9mbGNaSXd0cktjQlliQ0ZuRlpVT2txK2ZTcmdiMmZqN0xOcHNCY3h6bXdBL1dmNCtORzRrQVprR3dPRHV0SGpVS1NxYlVNSXhVYWVTaXBEbzVpeU4vT1k4aWVHTndyYVFKYWNKOEJ5ZkVNa1JjWVE1Qm5SNjdJd3RjRVRlaTlOZFFiSUxRbk1oUE43NnhGU3ZvY3JubVM2T2UrT1VuV0ZQWWh1QlV4MGdDUktQeTVoNmdnR21VRWUrNWlMeDM2UGpFRzAzSnUzVUQvdjI2RHhmdWJFalRmaVVkYmNnTHJLeGlMdlVqR2M4V1VFSFB5UlcxYUVrV0t4WkFURThoRDBHejBzNWNIVlBlVHQ5NXVTZmlsbkJ0eTBOclVrMkRYOEdoU0F1MFlaRE9PeDdWNGk5QkZ4YW45WjNsUlpOODZFd1d0M2tGTXY2SmZlMklFRVFvSFBjVllrWit0KzRMemp2YVYyNllzNkIzK0c0QnY4aHJvT3JMU0VrVzRoZ2hQOUFTWG1PM0lkdEdjTFBxSXhmU0ZzNUc2S3MiLCJtYWMiOiJlYWIyMjU4N2NmODNkNGU1ZGIxZjJmMTFhYjBlYWU2MTc1YmY1MjVlMWY1YzFmNGVhNmRlMDljNmY5ZjNkNWJkIiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 03:35:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkRrZTRhV01hN1dPY0h0YnVoRjdpRnc9PSIsInZhbHVlIjoialpCZEl4eVZGNWdlTnErYmpDU2VNL25MVGFvMzJNdFl4UWd5YjNWWnNuZWZiVDVNb1VVV2tIVnlYY1JWdmtHTy93R083YzNDNWJiYkIySEE3dkxheU9iQmhwZXcrZGIxMmlQMjFrOHpSZkVBN2tONFUyV0Q3cXBFZjNoeGJ4SldZRjN0dWRDVHIrN2tmbkIvLzdEbXpvYnBxRDNEdWV0dkk4b1ZSRWtKL3RBTUtFZ2h1SktRczFsTFFnM2N5YTVvZmJNUTNzby9VMzd0d0M1UkdXeUt5T1NkNmhkdXoweEpGNWxsR3RkNzFqeS9YNWZ4WTJYdWY3RHZqeVNDdGdXbHVFSW5QaFFENTBkeElIZnVCc1krbklCa090NW5kOXNRS09qWHJIOXhYYU0xWk10REJoSEtFMTFBMHdELzI1L2J6THFjSnZKZWF4VkNQb242TTVGZ2xyR1NTUWh4V3ZQV0NpL1FBNk1md24xTWRvRVVTNHZMeWprZ0p1SmpzTUVDSjVqd2ZBTXZQRzhjOTArY0FmN1pMQ0dQMjZIWUFtSXpoalpXYXhNZWk3NDJoVFQ3STRsSDhBSVhiaXoxN2tvM1pZLzFzbmY2RmdMeWtuTkgycjVBVkRTc081TXJuNGJpVnFwbEgxcFV1V1JnaisyUnNzcEFUMjIwUDdRWVhqZzUiLCJtYWMiOiI1ZjNlYWFlMGEyYmRhZmRmYjc3ODc1YzcwZjhhYzcwYmU4YzcxODdkOTMxZGE5MTZiZDRkYWQ4NTg4OTY2NWJmIiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 03:35:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2068733699\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-569005778 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OxCA9rABwnsUtdJrsRyx2DLpR7CHtGXPC1CLyExX</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-569005778\", {\"maxDepth\":0})</script>\n"}}