{"__meta": {"id": "X6ba7175d874a9a3e54b15ba3e9d29ac4", "datetime": "2025-06-21 01:34:22", "utime": **********.248513, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750469660.685096, "end": **********.248544, "duration": 1.5634479522705078, "duration_str": "1.56s", "measures": [{"label": "Booting", "start": 1750469660.685096, "relative_start": 0, "end": **********.045039, "relative_end": **********.045039, "duration": 1.35994291305542, "duration_str": "1.36s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.045061, "relative_start": 1.3599650859832764, "end": **********.248547, "relative_end": 3.0994415283203125e-06, "duration": 0.20348596572875977, "duration_str": "203ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46092144, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.03094, "accumulated_duration_str": "30.94ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.146958, "duration": 0.0275, "duration_str": "27.5ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 88.882}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.2044659, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 88.882, "width_percent": 4.686}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.2240288, "duration": 0.00199, "duration_str": "1.99ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 93.568, "width_percent": 6.432}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "bSqGKRzn8zrN52whBJ5jpetFrYHJ06phOnPEUsYY", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-302146600 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-302146600\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1570291815 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1570291815\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-706456948 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bSqGKRzn8zrN52whBJ5jpetFrYHJ06phOnPEUsYY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-706456948\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-823396335 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1833 characters\">_clck=h9hzj7%7C2%7Cfwy%7C0%7C1998; _clsk=1pfi5p6%7C1750469631526%7C1%7C1%7Cj.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImNLb1F5bUwrcnFtSmo1REozUW9Rc1E9PSIsInZhbHVlIjoiR21laVBNektOSzVDSy8xM1M4bEdtaXEwUEczYUd2MHVTcXI0dTJaQXZVT2doYW9XbWxoYmJUc3dxQXJIZXRMekg1STdvcWxodjJHeU0zUWVhN2wyUGNHcTNFWXdpYzZVUVBYWHlKRjkrRHlvUyswUHFEL1FIaTQxZTdCeWJxUExIUytpTHJ2ZFpYK0ZwcjUycDVXTUNVS1ZpaGc2eC9LTmJ0ZTQ1VS9xTWp6alVUU1h3c1BQVDRqUFZMSk9oaE5CZkFEK0EyR2NGOEJXMTMzMTRzODF5UElvY0d5QXF4WHB6aTRiTmdGRUFpRWhUbEhYUVpPSGxDMDlod0VtRDBadTlqbXkzR1plUnF5K3JwaWxBMllLZ1A0UDQ4OFpXNzdOa2E5Y2FMZzFoKy9DQ0xaaTZyWWJDeVF0Y1daN3A0a3FTQVlpTzN6NS9ZT2FhaWs5NzVaeThpSk1MZVFENjBDMFRHMk9XUXJJOTdHS1RVREZpZHpoU1M3UzY5cnZENzd3Z3pyR2JvU25QZDgzNVUrQWgwTDUxcGp3Y3U4TDNmbnFiV1hIMmF3UFkyYithbldqWGpqbDhtZzdqZ0ZuaE80eTFQWE1YOXc0NWJScXlvRm5KeHE2VDkxVndpVGMydEJxZlVObHVOdWdzVWZGTG1sVmZ5MVpIaHhSKzlFVWVKVDYiLCJtYWMiOiJhZGZmNDkyN2UxOGU5NzkwNzUwMjMzYjAwNDQ5ZjhkMzBlMzRjODAwYjc2OTA2N2Y1OTU3Yjc4MDBiYjliNzlmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Iks5MWFWRi9yZlFvUEh3WXBYNzdnUGc9PSIsInZhbHVlIjoiOU0wVWN4M3llMVN0WVNRVThUWlVHSGpZN0ZoblNSZ3FvNFppRjU4TjhqNWhESjJRRFhyVDNqbzBBMGM3cVM2b2ZJSDNWZXZ1Ly96NDlSaVZ4V3ZmRFBKc01obWR6dm1GZWRseDkyYjRVWnpiODZRR2o0aERVOFZxQ2hVV0RleDFEcVUvWnp6c2xwVXB1OFhuVlJraURoeTFJQkRHOVdoV3draEpFOFdRRVNoRlQwMU0wMW5GM0hXbjYzTUpvbDl1L2lONUZMZUt3eUxXQlViRGEvMTRoQzlUcGpoZ1kwRkNQMEpSeHdHVXJLaVRRVmg4S1lEbG90VHF6SmRXR0c2VlpwTXQ4bVVaVjdLS3Fqd09QczU2TDc0QncyTTkvL2R6VVBqY2lEN1k2cGVIQlhxQnZ3OU5BendqZE9RWUttUmhxdkFQSEpFd2FyNHhIdlUrVGlxRmpWYUJwRXNGN0FxcFl0eDdjY2JqRThmNUsxZ1VFLzJud0xvQzRXcjlCRkJ6OExhaTIvUU15YW9iZy96eG9adGJZNnNjbi9RVUdRbGd0ZU9zcm9SdXlnNVhCa0w2Mk1GUEo0UU4rbkxTUE8yQ1dLUGhYTWdVdWpiLzg2RmNrb21WaldGYWUwOERjNWNvK095WFhwRDAyTUxpZHRnT2dyZnRySExCWWFpc2pmSGQiLCJtYWMiOiI0YWI1YmNhZDYwNTA2OGEyMDI3ZGVlYmFlZmZjOTA0NjY5YmIwZjk3ODMyN2NkMjM3OWI5ODM4NTBkN2I5ZmUxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-823396335\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bSqGKRzn8zrN52whBJ5jpetFrYHJ06phOnPEUsYY</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HB3SpfsNW9qh15emZQ8PQBnZsYUESoWIqimBy8Yg</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-220945387 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 21 Jun 2025 01:34:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjdIcWpwQUxaNzEwNWhHYzlHek85TlE9PSIsInZhbHVlIjoiVnRhN2l6azV1dGN5MFZGaDNUTUFZM3dZenppdmo4SVc5N3Mxc1phSUlFRGpXblNERlhpR0pWMnk3c1FHNlEzRVpEYmloZUZZdTVJaHNWSUkyVXBwTDIwR0QxOFNwR1Z4YzRKcUZxaEdVcWhWZzdkcVZaSlBKY3RSMlNjbXR5VzRQUFVVK2RNc1Zhc0RLRklkZERxQjNUcGlEVmNDOWFYalA3Z3I2MXZvZXM1Y0RBZmpoaVBBc2V3SVpXOE8vYTQ4bTNSUW4wK3FObE13Wnp1eVRmbzNRSlA1VkUwb1ZWMjNmTVV5Zkt6NXBXTFdGOSt5V1IwZStTWHBRTjlESC9HSkJGRkgvb3R6d0FhMkdSTXdMTEpHVFZ2MFFkb1Zqczdya3RIOVR1Rnh1K2ZKNlNsazNieVEzNVVEWU96WGJDWER6OTJPenpTZHQzODQ3aElkVnYzNmwrTEFwOVVwcU9IVklzaXBjWXJGbkZXQUxVQVJLT29DVmxXQzd5YUFBczlaRGgzeGFHdVZqaUtUL2NDbllnRWdNMmt5dkpuNE1Kckpyem9sOEdzdHFsMERqb3BwQXNncnh1cDZkRzYrSzFZa0NOOEI2WllHeTE2Q0dIK3o5d0FGU011bGJwQk5nMXppRUJtNE83N3gwcnZLdDAyRUtZMkE4Y0lUUlROSVNRZmYiLCJtYWMiOiJjYTBhMjg2NWEwOGQ4ZThlMTBmOGZhNTcxNDdjYmQ1MTZlZGJmNjE2ZDk1NTRkMGI5ODFiYjIxNGQ3MTE4ZmZiIiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 03:34:22 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InBxMGw2d21jV09peVVhRzdpNWVrYnc9PSIsInZhbHVlIjoiRlpWYiswSlpRSm1neElBZFhNK1Y1cE5EM0x5QkNabWx3eCt0WUNDMEVNVkMwUmNzTEJuY25VM2N6NWppVzBodEZaTlV0Z3QwTlhwZjk0ZlNjbmR1YnQySmNwSXdCMGxvTEVxTUplNkM5VFFMNFQ2Tk5UQlpnOWJnNm1YU2swTDdndDFIblNGcnVqTmxySzdKTGZ4NWt6R2thdzNsengrUDk3WkcxVGtPSTRlc3U0WjQrNFZxem9OR0tGbVZoQkNvblNtNkVVdEpNZFptSTNqaG43NmZ5OXZMUkJVUkwzWHFmYVRRRzREc1ZSU0kwU2dCSUFLeFVvc3FxNEtvWlE2d3JHam5hVUYzazZmaWhRTGp1YWk0ZjJ3K0JKM2FRZ0Nla1ZhK25qcDhyVHlOeFVYS21WMUZuYytOV09Vb3ZUT0JVYnhteEFCQ3E4U1UyM01mbXptNlRYbnd0YTQ4K2d6MW5sNWx1Z2VqRlYweFMrRFZnMWUyZzZyVkRSbUx0SXhGUnN3ZThnN1ZoaGdnNTRqTXNqYVkrZXU2RFAvU2wyTWdWTjlUeDNLaFNGcmhVZ0h1UXkwakNiU3dXY0lQbDFhd2E5NUFqa1JxamNrSEd6dWhXdWpNS3JMWnpnbVJ6SUVST0FEa092ZHZ2TlBpNVBhc0tqZWc4a2lJUXF3cTlWbUYiLCJtYWMiOiI0MTY3OGYyODE3NTE4NzIyZGQ3NDBmOGU3ZDlhZDFmYTgxMGI0NzRmYjAzY2JiZWZlNTk2NjRkYmY0YjI4MTAyIiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 03:34:22 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjdIcWpwQUxaNzEwNWhHYzlHek85TlE9PSIsInZhbHVlIjoiVnRhN2l6azV1dGN5MFZGaDNUTUFZM3dZenppdmo4SVc5N3Mxc1phSUlFRGpXblNERlhpR0pWMnk3c1FHNlEzRVpEYmloZUZZdTVJaHNWSUkyVXBwTDIwR0QxOFNwR1Z4YzRKcUZxaEdVcWhWZzdkcVZaSlBKY3RSMlNjbXR5VzRQUFVVK2RNc1Zhc0RLRklkZERxQjNUcGlEVmNDOWFYalA3Z3I2MXZvZXM1Y0RBZmpoaVBBc2V3SVpXOE8vYTQ4bTNSUW4wK3FObE13Wnp1eVRmbzNRSlA1VkUwb1ZWMjNmTVV5Zkt6NXBXTFdGOSt5V1IwZStTWHBRTjlESC9HSkJGRkgvb3R6d0FhMkdSTXdMTEpHVFZ2MFFkb1Zqczdya3RIOVR1Rnh1K2ZKNlNsazNieVEzNVVEWU96WGJDWER6OTJPenpTZHQzODQ3aElkVnYzNmwrTEFwOVVwcU9IVklzaXBjWXJGbkZXQUxVQVJLT29DVmxXQzd5YUFBczlaRGgzeGFHdVZqaUtUL2NDbllnRWdNMmt5dkpuNE1Kckpyem9sOEdzdHFsMERqb3BwQXNncnh1cDZkRzYrSzFZa0NOOEI2WllHeTE2Q0dIK3o5d0FGU011bGJwQk5nMXppRUJtNE83N3gwcnZLdDAyRUtZMkE4Y0lUUlROSVNRZmYiLCJtYWMiOiJjYTBhMjg2NWEwOGQ4ZThlMTBmOGZhNTcxNDdjYmQ1MTZlZGJmNjE2ZDk1NTRkMGI5ODFiYjIxNGQ3MTE4ZmZiIiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 03:34:22 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InBxMGw2d21jV09peVVhRzdpNWVrYnc9PSIsInZhbHVlIjoiRlpWYiswSlpRSm1neElBZFhNK1Y1cE5EM0x5QkNabWx3eCt0WUNDMEVNVkMwUmNzTEJuY25VM2N6NWppVzBodEZaTlV0Z3QwTlhwZjk0ZlNjbmR1YnQySmNwSXdCMGxvTEVxTUplNkM5VFFMNFQ2Tk5UQlpnOWJnNm1YU2swTDdndDFIblNGcnVqTmxySzdKTGZ4NWt6R2thdzNsengrUDk3WkcxVGtPSTRlc3U0WjQrNFZxem9OR0tGbVZoQkNvblNtNkVVdEpNZFptSTNqaG43NmZ5OXZMUkJVUkwzWHFmYVRRRzREc1ZSU0kwU2dCSUFLeFVvc3FxNEtvWlE2d3JHam5hVUYzazZmaWhRTGp1YWk0ZjJ3K0JKM2FRZ0Nla1ZhK25qcDhyVHlOeFVYS21WMUZuYytOV09Vb3ZUT0JVYnhteEFCQ3E4U1UyM01mbXptNlRYbnd0YTQ4K2d6MW5sNWx1Z2VqRlYweFMrRFZnMWUyZzZyVkRSbUx0SXhGUnN3ZThnN1ZoaGdnNTRqTXNqYVkrZXU2RFAvU2wyTWdWTjlUeDNLaFNGcmhVZ0h1UXkwakNiU3dXY0lQbDFhd2E5NUFqa1JxamNrSEd6dWhXdWpNS3JMWnpnbVJ6SUVST0FEa092ZHZ2TlBpNVBhc0tqZWc4a2lJUXF3cTlWbUYiLCJtYWMiOiI0MTY3OGYyODE3NTE4NzIyZGQ3NDBmOGU3ZDlhZDFmYTgxMGI0NzRmYjAzY2JiZWZlNTk2NjRkYmY0YjI4MTAyIiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 03:34:22 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-220945387\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bSqGKRzn8zrN52whBJ5jpetFrYHJ06phOnPEUsYY</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}