{"__meta": {"id": "Xd5ad4d25a11ca54775abb20a77a324b9", "datetime": "2025-06-21 01:34:53", "utime": **********.612597, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.365313, "end": **********.612623, "duration": 1.***************, "duration_str": "1.25s", "measures": [{"label": "Booting", "start": **********.365313, "relative_start": 0, "end": **********.436687, "relative_end": **********.436687, "duration": 1.****************, "duration_str": "1.07s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.436708, "relative_start": 1.***************, "end": **********.612626, "relative_end": 3.0994415283203125e-06, "duration": 0.****************, "duration_str": "176ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.03267, "accumulated_duration_str": "32.67ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5122402, "duration": 0.02978, "duration_str": "29.78ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 91.154}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.5652392, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 91.154, "width_percent": 3.52}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.592509, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 94.674, "width_percent": 5.326}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "OxCA9rABwnsUtdJrsRyx2DLpR7CHtGXPC1CLyExX", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-5914511 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwy%7C0%7C1960; _clsk=1updagk%7C1750469691324%7C4%7C1%7Cj.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik5xWi9OS2xtbjZsUmltNUpaQktrYUE9PSIsInZhbHVlIjoiZ3RpajhvakV0eEIvRzEybnpvRGtMc3hlZmVXNjB2NW4yVDRqc0tra0p5Q3RjWCtkOG9zMytQWTN1STd6YU94a09ramNEaEQ3OXJPNFFhWWlQQzF2ZWM3Y0YvaC9PZExTMmUycFJVbnVtTG1vQnJJMFR2UE9SYWsxRk5mWnNMWEgzWHdZNHNjZnF3eEhXLzhSb2xJenBsOVgxNnlUWGJiK0xzc2x5T2JTYkUwNHdzcmQyTEVNVVBETmtnT1JwSEZhZWdXSFMxR21RTTE0ay96bGlLZTNJR0M5MEVNYnVYRzhjZEUxYUt2MVVBMmN1dGU1U3oxK2dQYXlPRHEyL2VHc0dSTDJFVmpZcTZ6Y0V5NXBDWVhJM0hac0NSb0p1M1VjS2NGNzdSazFkcVc4bk45MWRpQjc3NTEycyszZ3pPQmtiUzVGSU5WTGNGbTAwcWUzUmM1YVpVaWdvUHN3SnpnTWd4akNWdWF6RENsWEYyOVBRZ3U4WTJobWpMZlgwSkZ5R1FiKzNSR0pQNkJTVGU1WVVVR0E1Zm8ra0tlTkxVdzUzUWlPR0ZlSHR1azJBWHdwRnBVNDZUeTJoY0hjVEp1V1IzN3J4S01QTmVMcDhrUmtoZElabFU4VERUa1hKMTRnSFRCT3FVY1cxQU5VV3RTaGx3TSsvODF1cC9KQjlzRTAiLCJtYWMiOiI2ZjI2NDM3MTcwNGNlZmNlOTE5NWEzMDliYjdiZDBhMzk2MjVlOTZhOTUxYWZlOThkZWRmZGU4MDM1ODFjMjEwIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkZVSkUyVUc1UWtOaUplSWl6WmxLbFE9PSIsInZhbHVlIjoiSUhhZ3FJYWxrSzhIdGZvS3hWUExlNnpldXppdXBFc0NRTWlvZkVYMDV3LzNyNDAwRzZsMVA2ZVFheEVLdS95RVgzY0g2R1duSHFoN1RzMVBJbHQyOFhobWNwZ0h3anplRnRQQjhiS0JPbHdqVndNQTBLc2JGc0J0dHlFY2hYTXhGVVZpTEpZTWRCSml6Qk5yeUp2bGwzVVdMazV2R3c1Q2FMcndFdi9jYTB1MVhnVUtITGpPbDRWRHNWekVEM2twU21lUmZUbTNwR0ppaXdhb0RxR2dIWTF5Qm4wSzRwZXJnSWw4SFRqUHFzYTZYejhka2VlTjlKZGNBQU5DQzRsQjk3Z1FNUnBkMk8rbXZqS2RTaVlSNzBpb3MzcTNTMHppWHVZc3hZTWxFYTdZVWtBV09YNFBXODRvcm9rVjVsQzVmV1dLRjhabURkR2g3cGFoOXQ3MjZqSnphbTQvQmcrTXo3VFpKTlZ3UGRpeU9QaEpDSVNEYlNNUHA0UG9iTzRWaGhmUzFGa2prblJwdTZocVZobnovUGVyUTBPRlBCTk1sVEFWUU9CTVQ5Q3plUElEaUtrcVpSSk0wL21tb0Q3ZmdCWXk5aCtGY1NrcDgwajJFYUJCN0VMVXlkYkJQRElmZldodWxGc29TU0ZoSlJ2RmZncmlkZHlUUEdoNmsycmsiLCJtYWMiOiI5YWVlM2RhOThjYzUzMjVmY2FmZmVjNzg0M2JmZDllOTgyMmRmMTY3NjYyM2JhZjIyMDU1MTJmNmUzOGQ1MzQ1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-5914511\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1366769940 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OxCA9rABwnsUtdJrsRyx2DLpR7CHtGXPC1CLyExX</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">h4v7KnclY0iOKyPMFu5GxMLqVTN5plmJ6SYNK0tY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1366769940\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1837851643 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 21 Jun 2025 01:34:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application, X-CSRF-TOKEN</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InUrTjNkOVJ5U29SRGUrUm85U3drTlE9PSIsInZhbHVlIjoiWmRIY2JCMVEvY1g5ZlRhOFkwY3FpU1hHVmNkQ1QxVGtoQ1lxZUtRS3dobzlPM3VRM2I3S3FkWU5pSHIwTG5nUnRua3RnOGhJeXJpTTY4cW9oUTlKZEZKSmtraUcxM1RSTWdzSDErTis1Q0NTYW9DMmVvKysyTkRBOUhQSTE2UFN4ZXlHbERZRHh5YlpLbDZ5WGIxM2J1WEQzdU9UdXFweHRvd1BlMDFzdmt0QzVrUURlSDQ1SVBUcE9rUmpvVnRNNDZmd280QVBLQTRHL2t4SzBYaHBsNzFxSG95YVpqMHhiK3FKcHdmR3cyYlZVZGhWa1NWeW5LWkFGREQreURPRWNGdUhOT0c3bk1lWnV1ZTRrQjJ4d2JmeWwvRS9mczZuZUVPcmdnY2Nid1BEdGxCYzJXWTNXU0hkTzI5QUlHalRGRCtnUW40U1lRYnJ2V0tpcHg2MDVkdkUyYnpqalVpUFh2dFg2KzV1bEZzT3BEUGlvazNUNFhrZmwycXVkNDB0VE56WDB3OEVBeFRQbzA2bi96dHRzTVZpOWdLT0dnUTdqY000S2xmWFpaVHpSL0VyV2NPRmpDOERYS09ZODZWSzJUMlR6MHEzeGRhL1BrWHpIN2FxcWpVUFBZakFaYnp5b21nYi80SlZiOWFORGl6d0hzY1lzUUExSWxuRUVnTFAiLCJtYWMiOiJmODg0Zjk2M2UwOGM4MmJlY2JlOWUwN2Y1YTQxNDNjNjBhZDlmNTRjNWMxZTgzOGEwY2RkODQ2YzkwMDVlMTFkIiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 03:34:53 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImlacDFvZzAzeEZFYjF6Y0NxdXdjZlE9PSIsInZhbHVlIjoiNXp5Ri9HR0xOUDgzMzQzNHNqUEx1eURtVGRwMmI3QVVNb0JHOHZPRjZPdWplYUhlKzYzS1d5bHZnQkFlNThGTnU4cmZoRXUwNXcvaVZEajBQS1FWMHEwclZrQjFvUnBaa2pSd2RMT2lDam4rYS91UWg1MWNLVitOOEE4RlZOcDVHNjJ0UDZQblVhcWwybzlQUHRtaHlNblVheXBRenB2QU9PQWVSM0FyQjRJRnZqS01LSVlQV1ZpVHQwdkd0Z0RlODV6UVI0c3NMR2tRWEJJb0R4dnV6cWxETnlWNlFTYlRPd0NWK2tPRHRxZXo5WmZTSDJKM2pUNlVRVCtabGpPejlscXBydzVxcEU5YzBqbGVTL29nYU1oTUZFdndmby9vVmtpaXBKWEsrZmRpM3NYL1hzUDVpV29CaFZrNm9ibWJ4YXVrS056MWJHU3R5Sm43TGJySHF1VHRRQklsbGpobjRtbjZoQS9kT0svUFJvM1piSWdjelltL1M5aGxwd1M2YWtVTDlWNnRlaTFCS0dTc2R6L1kwRGNWZXhNbVF6UVZqQjN5dFRXR3pqcFl2c2YyNm40NCt2ZnJJR1VzczM2ZzZoZkZBd2RyOUJqTlhrSzlVTk0yeHVidFZ5aHBsWk4yUzBKejdGRGkxUUtLaWhIMVlFTk5NL0xBQjJEa21Kd3EiLCJtYWMiOiJiMzY5YTA5ZjI1MDNhNmQ5Njc3OTA4OTEwMGZiNmU1MzcxYjAzYjhkNmI0ZTkyNWU1YmFmNDk1YjU3NWJlMWIzIiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 03:34:53 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InUrTjNkOVJ5U29SRGUrUm85U3drTlE9PSIsInZhbHVlIjoiWmRIY2JCMVEvY1g5ZlRhOFkwY3FpU1hHVmNkQ1QxVGtoQ1lxZUtRS3dobzlPM3VRM2I3S3FkWU5pSHIwTG5nUnRua3RnOGhJeXJpTTY4cW9oUTlKZEZKSmtraUcxM1RSTWdzSDErTis1Q0NTYW9DMmVvKysyTkRBOUhQSTE2UFN4ZXlHbERZRHh5YlpLbDZ5WGIxM2J1WEQzdU9UdXFweHRvd1BlMDFzdmt0QzVrUURlSDQ1SVBUcE9rUmpvVnRNNDZmd280QVBLQTRHL2t4SzBYaHBsNzFxSG95YVpqMHhiK3FKcHdmR3cyYlZVZGhWa1NWeW5LWkFGREQreURPRWNGdUhOT0c3bk1lWnV1ZTRrQjJ4d2JmeWwvRS9mczZuZUVPcmdnY2Nid1BEdGxCYzJXWTNXU0hkTzI5QUlHalRGRCtnUW40U1lRYnJ2V0tpcHg2MDVkdkUyYnpqalVpUFh2dFg2KzV1bEZzT3BEUGlvazNUNFhrZmwycXVkNDB0VE56WDB3OEVBeFRQbzA2bi96dHRzTVZpOWdLT0dnUTdqY000S2xmWFpaVHpSL0VyV2NPRmpDOERYS09ZODZWSzJUMlR6MHEzeGRhL1BrWHpIN2FxcWpVUFBZakFaYnp5b21nYi80SlZiOWFORGl6d0hzY1lzUUExSWxuRUVnTFAiLCJtYWMiOiJmODg0Zjk2M2UwOGM4MmJlY2JlOWUwN2Y1YTQxNDNjNjBhZDlmNTRjNWMxZTgzOGEwY2RkODQ2YzkwMDVlMTFkIiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 03:34:53 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImlacDFvZzAzeEZFYjF6Y0NxdXdjZlE9PSIsInZhbHVlIjoiNXp5Ri9HR0xOUDgzMzQzNHNqUEx1eURtVGRwMmI3QVVNb0JHOHZPRjZPdWplYUhlKzYzS1d5bHZnQkFlNThGTnU4cmZoRXUwNXcvaVZEajBQS1FWMHEwclZrQjFvUnBaa2pSd2RMT2lDam4rYS91UWg1MWNLVitOOEE4RlZOcDVHNjJ0UDZQblVhcWwybzlQUHRtaHlNblVheXBRenB2QU9PQWVSM0FyQjRJRnZqS01LSVlQV1ZpVHQwdkd0Z0RlODV6UVI0c3NMR2tRWEJJb0R4dnV6cWxETnlWNlFTYlRPd0NWK2tPRHRxZXo5WmZTSDJKM2pUNlVRVCtabGpPejlscXBydzVxcEU5YzBqbGVTL29nYU1oTUZFdndmby9vVmtpaXBKWEsrZmRpM3NYL1hzUDVpV29CaFZrNm9ibWJ4YXVrS056MWJHU3R5Sm43TGJySHF1VHRRQklsbGpobjRtbjZoQS9kT0svUFJvM1piSWdjelltL1M5aGxwd1M2YWtVTDlWNnRlaTFCS0dTc2R6L1kwRGNWZXhNbVF6UVZqQjN5dFRXR3pqcFl2c2YyNm40NCt2ZnJJR1VzczM2ZzZoZkZBd2RyOUJqTlhrSzlVTk0yeHVidFZ5aHBsWk4yUzBKejdGRGkxUUtLaWhIMVlFTk5NL0xBQjJEa21Kd3EiLCJtYWMiOiJiMzY5YTA5ZjI1MDNhNmQ5Njc3OTA4OTEwMGZiNmU1MzcxYjAzYjhkNmI0ZTkyNWU1YmFmNDk1YjU3NWJlMWIzIiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 03:34:53 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1837851643\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-285605150 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OxCA9rABwnsUtdJrsRyx2DLpR7CHtGXPC1CLyExX</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-285605150\", {\"maxDepth\":0})</script>\n"}}