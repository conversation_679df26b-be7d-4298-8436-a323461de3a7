{"__meta": {"id": "X95247e700b12be3fcb541539b80dc684", "datetime": "2025-06-21 02:44:04", "utime": **********.942112, "method": "GET", "uri": "/", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750473840.280998, "end": **********.942157, "duration": 4.661159038543701, "duration_str": "4.66s", "measures": [{"label": "Booting", "start": 1750473840.280998, "relative_start": 0, "end": **********.546535, "relative_end": **********.546535, "duration": 4.2655370235443115, "duration_str": "4.27s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.54657, "relative_start": 4.2655720710754395, "end": **********.942161, "relative_end": 4.0531158447265625e-06, "duration": 0.39559102058410645, "duration_str": "396ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46195944, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@landingpage", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=59\" onclick=\"\">app/Http/Controllers/DashboardController.php:59-74</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00975, "accumulated_duration_str": "9.75ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 341}, {"index": 21, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 26}], "start": **********.7900732, "duration": 0.00819, "duration_str": "8.19ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 84}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.897484, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 84, "width_percent": 16}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "OxCA9rABwnsUtdJrsRyx2DLpR7CHtGXPC1CLyExX", "_previous": "array:1 [\n  \"url\" => \"http://localhost\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-238910656 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-238910656\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1514973658 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1514973658\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1424500712 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1424500712\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1085117727 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=nups8l%7C2%7Cfwy%7C0%7C1960; _clsk=1updagk%7C1750469741656%7C8%7C1%7Cj.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlZmS0lJeW96QWJiN1ZHa3IwYjQ3d2c9PSIsInZhbHVlIjoidnN3OGsvV2JDdWRrU1NSamMyR3BwMm9lTWw3UmxlNFYrUzNBNlA0dUN1VFIrV0FId3h6Z2t3eHNQL3hZeCs1QU82d2lJWnpGSzJWWE9zd1MyN1k5NGFpMXhHb0tZV2orRGhkODJtdUMwMlZBaGMwc3g2WEMvT2V4ZGJEckNZMVpzVnRpdHl3NVZwdCtmOFhvdnNKT2gwUjNCTjBtZThaMXNaWkpVcE54VC8vclJORHp0QXRsd05CWDFmOEc1cVdsYjJiOFVYWHFMbkRvRGUzZXBXdXhuUGx5QzdubXEvbjg2QXUzUVZNdzlrU0I4MmRFTnlXd2JIbU9JaTFtdEtQNmR0Z0NTK2J4SjlINFd0YlpkckJIcjQ0dDJPR2lVeENmYkcyVnRyVnU0OG8ydTFHRkV4T2lZSGJaQ2hpRDBwM2FCcFVGSlpEQzVMTk5MUlVuMk5BTisxN1VaT1Biai9nU25hSWNsL1cyVHQ0d2RaK3hXZHBHOTB4dDdxTi8xMzRhdnArY04yamVmZTNnUitsNWwxRzZXT2xGTk00cFBkQ2N6ZHZhZ1JSQ3I4RjhKRm1aNnorWks3cWlHTHAzMjc1ODlBTWdqMVN1MUVrNERqWDZ2ZjNIR3RDNHpLSFV5eE0wNzZPTjcyTzVHU2w4SjE1cXpxNEZKOFlvY3FUOXI1WlIiLCJtYWMiOiJhNDg4MWFlYzc0OWFjNjhmOGI3YzAyZjUyNjI5MzBiMDcyMzczMTQ4ZjliYzZkNWUwNzUyMzEyYzNiNDIyN2VlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ilc2TStFRDVNTnRrQnIxcmVxYmpabEE9PSIsInZhbHVlIjoiZWR2UUhSK3c0R253ZUkxV0NVdnlveHIzQXE2R3hucXZ6Nm13SVpCZVQzdDgrOCtodUo3Nk10RzJTVVBMRGJZbGxUM2RWVGNON0VYUm51NmxaaUFqMDdISThDRkxualJiaFpOUmc2Rlc1cTFuZUhoRCtHRm52QXN5TFoySithVHI0NDdTMmxxQXFka2NNaGJQU21oYjcwTTJWdWJ4TWhYNXp0K2dSUjQ2VVh2bUg2WEtKdFJuMDhrdG0zNlcrS1czOWxManBudmx4K2l5T0MyeHB1NnAwQmdFTi9wME9UVkJBd2xXNDdrODQxSFlIVFdjMUFNT2gwZUgvRHJwdzMzWndqei94ZjZSd3BEUXR0VUo5UkxWRzI5NE5JRy9nSlk2Rmc3TWluWU9hb0h6RVRlb3cxZldjN3ltQTBZbFU1c2xvTTJLT01TN2dBeXdKMVZRQkt2REhxQnJUWTlnZGFvNGlpbmRmWlE2UEZQZFAzZ1VLdGhKakJTcWg0SFpLendpTkQwbG03UFY4dit4aDhlb29uYmJWT0wreUZibk9qTmdwUTFzZ3EvQkxsUW1mcUJZWUFqSno4VDNiQjhmWk1BaGhObFVndFN2S2l1N3ozL1grZGEzeTFnVWV2UnRFLzRkRzNUNmJiMDRqbm9BdndWS2MxU2VlZlJlZVRYSmZVbS8iLCJtYWMiOiIzMWEwNjVmNWExNDFmZjM2YzA4NWRjZTJjNzk3MWMzMmNhNDAyM2I1ZWFhNzBlNDVkMTE5OWJmMThhOWFlY2ZlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1085117727\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2056938553 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OxCA9rABwnsUtdJrsRyx2DLpR7CHtGXPC1CLyExX</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IqIQb9J95tflkp3hIkSidGYurblANYNn28mhR1bv</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2056938553\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-413372874 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 21 Jun 2025 02:44:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IndqQ29qem5oNDlTV0NHT2RURmluTEE9PSIsInZhbHVlIjoiVjJIVnV4MjlSYnBMNElib2gyK2NQdC9GRSt2OFVWb3p2ZWUwWHlEczZrblhmYmMvamdua0tZYXBrSjZWT2NYTG9iTHNLTDN3UVdzU3R2cFNvQVlwbTI4YTZaSElwcTNWaHo1YldlQ01ZUnZOcVRBMXU5YUhVL0NNZUl0RFhLWjY1Zys1MTVVaWkzSmpWNTl6VkVuWkdtTkNWcjdZSmhKc0toTG96SGZXdVBsMzNmTDAxaGFBK3MyNDltRzh4bzN1V1haR1gvYy9icVFIQ1IvNjIrMXZKeEJPaFJLREVJU2NlUzJkaG94Z2pmcnFGNGd0RWU4K1ptTER0V3V6K3d3QlcvbEloYW1xRjVNd1lOSm9QZW9hVG1CbnZ3SlZYZGZrdDYzeFVaWHpJNHpQWVRvVEFQaUd6VGtPWm1qOE1RcVAyZ1FTdmpyVURKdFBUZnBPanZFR0FWOGVDK01uZjJ0T1RRWFNaRE5xVVJzSmVXOG5vQ25RTUlNYnJOZ1B2RWR1V2t4bTdBaEFmdW9CbnM0YTdhS3pwSVNzeGVNZW9HcVRQM29OSGIwS0lZOVVCc2RwZ0xiOGFMdWs2QVcwNGIxenVPckk3OUZjTWErOFU4V0ZLUWlJQkhFL2RhNVE1VFZXR1J1YkEveVp0Rll2Q3ROMFMvejBUdHdRL2RBSStidWkiLCJtYWMiOiI4NzMzZmQ4Njg2MGQ5Y2VjYzY3OWFiMzdhZDQwNmE3NGQ5ZDllY2M5N2U1ZTQyZGE1MWEzOWMyYjllN2FmNDhjIiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 04:44:04 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkFueitnaEhVZ0gzdVVORTh6ckJ3c2c9PSIsInZhbHVlIjoiU0hZWlhiZkRweSs4T3pDcm5NRk1SYXZ2Y3Q4aGJ2NUlDVDZONlFhM05ZT2RIK0NHUkwydFV6bUNYK2c3bnlSK3Q5SU4rdi9aZlpoaGt2QngxMm9tVnJFVnJNU3RNcHlaaVA1dWgxMTJNMEw2K251a0xOVGpFNDV0aExhOVIxeXErMGJUUmFobXVRTDZNcklkQlAzVmlSVENLZFVWOTJoaDZCWlIxd0JONlZLRmgzdzFseVJ2RGs0ZWZHb29tL3NOd2plNnhLU2tlbElzNlhlaklIMkM5NnFoZGpyTUVzbERrbmdVWU53TVZyQmlQRWh1Nkl5b211aGY2ZDFPNXBwbUNZQ3hiZEhKclRseXJmcnRBbGZ3cmxVWkJSYWJiR2F0RlREdmdvZE9uV25tWncwaWYzaEpuVWtFazVPNmFRR3lHM0xIM2ZiNnRlbUJCaWhQTU16K2RKZ01PcDdYdDhRWXZFbFIzQVp5RDd4YWlGZS9UVkt5ajlGSGtINXZsVlUva3lERm1jT0lIdS92WXFZWU1pNlpaK2xVRDB2UWtxQ2Y1V21aakpGbTlZWG5XUUw3dnA1VzNCYXBqQTFYQVZUdXkyZ1hrSWVsZmlkUjNrNUs2MFRRcXlpL1hSYVFUMTgxOGRrbHNCVkRNWTdtUGNlVHdGZUU5czV0RUJoSytCM0ciLCJtYWMiOiIwNTdkNTEzMmQ5NzAyNzM0MTQ2ZGY0YWE2NjQ3ODczMzA0NWJkOGIyMWFlMzNiOTI0YjQxZGVkYzU4NTE4YzNmIiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 04:44:04 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IndqQ29qem5oNDlTV0NHT2RURmluTEE9PSIsInZhbHVlIjoiVjJIVnV4MjlSYnBMNElib2gyK2NQdC9GRSt2OFVWb3p2ZWUwWHlEczZrblhmYmMvamdua0tZYXBrSjZWT2NYTG9iTHNLTDN3UVdzU3R2cFNvQVlwbTI4YTZaSElwcTNWaHo1YldlQ01ZUnZOcVRBMXU5YUhVL0NNZUl0RFhLWjY1Zys1MTVVaWkzSmpWNTl6VkVuWkdtTkNWcjdZSmhKc0toTG96SGZXdVBsMzNmTDAxaGFBK3MyNDltRzh4bzN1V1haR1gvYy9icVFIQ1IvNjIrMXZKeEJPaFJLREVJU2NlUzJkaG94Z2pmcnFGNGd0RWU4K1ptTER0V3V6K3d3QlcvbEloYW1xRjVNd1lOSm9QZW9hVG1CbnZ3SlZYZGZrdDYzeFVaWHpJNHpQWVRvVEFQaUd6VGtPWm1qOE1RcVAyZ1FTdmpyVURKdFBUZnBPanZFR0FWOGVDK01uZjJ0T1RRWFNaRE5xVVJzSmVXOG5vQ25RTUlNYnJOZ1B2RWR1V2t4bTdBaEFmdW9CbnM0YTdhS3pwSVNzeGVNZW9HcVRQM29OSGIwS0lZOVVCc2RwZ0xiOGFMdWs2QVcwNGIxenVPckk3OUZjTWErOFU4V0ZLUWlJQkhFL2RhNVE1VFZXR1J1YkEveVp0Rll2Q3ROMFMvejBUdHdRL2RBSStidWkiLCJtYWMiOiI4NzMzZmQ4Njg2MGQ5Y2VjYzY3OWFiMzdhZDQwNmE3NGQ5ZDllY2M5N2U1ZTQyZGE1MWEzOWMyYjllN2FmNDhjIiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 04:44:04 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkFueitnaEhVZ0gzdVVORTh6ckJ3c2c9PSIsInZhbHVlIjoiU0hZWlhiZkRweSs4T3pDcm5NRk1SYXZ2Y3Q4aGJ2NUlDVDZONlFhM05ZT2RIK0NHUkwydFV6bUNYK2c3bnlSK3Q5SU4rdi9aZlpoaGt2QngxMm9tVnJFVnJNU3RNcHlaaVA1dWgxMTJNMEw2K251a0xOVGpFNDV0aExhOVIxeXErMGJUUmFobXVRTDZNcklkQlAzVmlSVENLZFVWOTJoaDZCWlIxd0JONlZLRmgzdzFseVJ2RGs0ZWZHb29tL3NOd2plNnhLU2tlbElzNlhlaklIMkM5NnFoZGpyTUVzbERrbmdVWU53TVZyQmlQRWh1Nkl5b211aGY2ZDFPNXBwbUNZQ3hiZEhKclRseXJmcnRBbGZ3cmxVWkJSYWJiR2F0RlREdmdvZE9uV25tWncwaWYzaEpuVWtFazVPNmFRR3lHM0xIM2ZiNnRlbUJCaWhQTU16K2RKZ01PcDdYdDhRWXZFbFIzQVp5RDd4YWlGZS9UVkt5ajlGSGtINXZsVlUva3lERm1jT0lIdS92WXFZWU1pNlpaK2xVRDB2UWtxQ2Y1V21aakpGbTlZWG5XUUw3dnA1VzNCYXBqQTFYQVZUdXkyZ1hrSWVsZmlkUjNrNUs2MFRRcXlpL1hSYVFUMTgxOGRrbHNCVkRNWTdtUGNlVHdGZUU5czV0RUJoSytCM0ciLCJtYWMiOiIwNTdkNTEzMmQ5NzAyNzM0MTQ2ZGY0YWE2NjQ3ODczMzA0NWJkOGIyMWFlMzNiOTI0YjQxZGVkYzU4NTE4YzNmIiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 04:44:04 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-413372874\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-74919600 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OxCA9rABwnsUtdJrsRyx2DLpR7CHtGXPC1CLyExX</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-74919600\", {\"maxDepth\":0})</script>\n"}}