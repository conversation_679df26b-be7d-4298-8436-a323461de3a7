{"__meta": {"id": "X8b69255f71b4a107a1d0d3bc63afc565", "datetime": "2025-06-21 01:35:28", "utime": **********.762379, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.438985, "end": **********.762421, "duration": 1.****************, "duration_str": "1.32s", "measures": [{"label": "Booting", "start": **********.438985, "relative_start": 0, "end": **********.57616, "relative_end": **********.57616, "duration": 1.****************, "duration_str": "1.14s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.576185, "relative_start": 1.***************, "end": **********.762425, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "186ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02619, "accumulated_duration_str": "26.19ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.660121, "duration": 0.023, "duration_str": "23ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 87.82}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.708618, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 87.82, "width_percent": 4.735}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.734632, "duration": 0.00195, "duration_str": "1.95ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "ty", "start_percent": 92.554, "width_percent": 7.446}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "OxCA9rABwnsUtdJrsRyx2DLpR7CHtGXPC1CLyExX", "_previous": "array:1 [\n  \"url\" => \"http://localhost/forms\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/forms</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwy%7C0%7C1960; _clsk=1updagk%7C1750469725331%7C6%7C1%7Cj.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IktOZUJWSXVFV09kdWR6THVzM3VUMmc9PSIsInZhbHVlIjoiemxTTldZaGJ0OExybUp3bzBzT1VPZnplRHlrRS9xZytOSEZGMnJxL0lWakMvR2RidEJ2R2xCOFl3em9xc3Z6TW9sVDh3TFk1TUxJeXdSeFpDVzR6SUhXeE10M0RxSTIvZEZPcWE1S2p6bThpOXBJUTlMcitKUENITWxOYzlldGFWUEVmRjdPZmNJSDdOYXdOTjFEQ2J2MVFHUDg1M0Fzd2hYeWhSSWp6SHloZ1pUR1M1Um81ZkhvYk5Bbkw3K2N3amhJTk5saVM3YkRSODhUcklFZkxJYTBSTWk5ek1DOEFHc3V2aktSNTA4cWZ6T3VGWGUzem8xOFpjNURyeFMxUGZFUDJ4VWNSdlRqeVAxcjJJUVgxRlNpMTdZYTFOdHlpQldVb01iSGxqL3Azakh1QUEyRkNORFQ0eENSTXYxMERQMHNIblY5TCt3b3ovSGwwbXhaTng4MmdNSDZvV2ZiOGVmbkpMVDQrYUpmeXNweUtab3JMMGtVNjMzQmNkbjdQcGdxTlROUVVMZjRUdUIvWHFlQTVCWEYvUkRQdHBybC9MOUZBbVpVd3R3ZVZQMmRwcnc3OVFUTnRsby9EMlF3dUlpN1V3VXpkMmlKbzNmQlV2OGpkV1RTazlSdFQ1RkdNMUo1aDMvU2hMLytzNnpOQ3RYMEMwemJFWFZlVHlYK28iLCJtYWMiOiIxYmQxMjQzMmY5ZGIwYjVjMjY5YjQ2ZDNmZjIyYjIzZWUxYmJhOTdmMzM1MjE4YzVmYzA3MjMwY2Y0YTE3NmEwIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InhWaDBsRG1JRFFTT2ZBOHpvd0VGOUE9PSIsInZhbHVlIjoiSWNPTFpyMTUvbGxWaDJ4elNEa1NwTm5mSVU4R3NKQlVUNHJoMGRka3FSbkpMRVNaeTdDWndCT2tiZUF0eC9KcGxzWmswdWxNek9TMjZNbk9NU0xvOXdKNXk5S2M5NDZ3emIwUHZwOVcxQkVHN1N0ck5QSU9YbHgvUEQ1U2FVWTUwMlp3UkNiN1VPV3ZkK1V2dFNSb1paSGFLbGdFZThEU3M0YmNKN2tDSFFid1dLYzlkY0NjTFRxY2FFYU8vMVFKWVNZWnpNVEhWajFiMFNBTi9Zd095S1hjZzZ5ZmV2MmNKenN6UGFaR2pucFBoN1FvNFAwdnNMYkRCeG9QVVg5am1RR054T04wbWJMMDVBZDREZzZiUFhuWkhzQ3lNSWRUVXEvUHF6L1p2VGltTDlmSWNwT3c3YUhzZ2pDeGZBQm4rRG5aM1VnZjNZcytzVWxha1JtNld6dkdSVmZZM3hZWGNWbjNJUUduTWtheDB5R0VBcm0rdnNQTDZnTjVDTnlVbElHK3ZqWW5IOFozMWpQaUdWeDJJcjV0VzBtdUZtSDd6dnNIT3JhMjhYZEJCeU1FMlRVd3FvMWFROUxBMVk3Vm90dlRyN1JFMmNzbnhsWmc2R3MrN2w5UFBoeXNiNWl1TTBmcU44bHlIT29FenBTUXZ1QWRuMUJET2dSUHBoU1UiLCJtYWMiOiJlN2Q0YzhjNTJiODJhZjVhM2MxOWM1NDUzODM0ZDk0YjJhNDMxOTVmZjAzNmZkOWU2Y2EzNjA4M2U4MGJkODAyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OxCA9rABwnsUtdJrsRyx2DLpR7CHtGXPC1CLyExX</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">h4v7KnclY0iOKyPMFu5GxMLqVTN5plmJ6SYNK0tY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 21 Jun 2025 01:35:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application, X-CSRF-TOKEN</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjVvS3VndFZZa0F2dmtYa0ZDRVJQU3c9PSIsInZhbHVlIjoieVp1aERXNW5BL1UyUnlLTUZhOXNocHJUd0o2NFBDKy8rckk5SkZNYjk5cXM3clBVNjNLN2FTQ1dPc3pQOU5QcVUxMDFxRmNYWi85RWZOa3JTdnphR20rNlcvd2YvSjRGdytjNmRPQzg3aTcxNjg5SEd4bUgxTWFsODNEamppZ3B6S1gvaGRZTW9PcTBBaUQ4TGw3U2pNOWg4b0pJUk9jWjNHVXB5UGdqemQ3LzJZc0Y2cWhLOWdPYWY5czJpK3VnM2tXRDhQN3JQc2FIV0FrNnBQWFAvWkJFRnRmN3kyZ200cEVWR2pPZHZ5ZHhCMktVY1lVY2Z4cVJVSTNmUHQ2K0R5NHNidkMwRlJCU0U2ayttdlpNeXpyQ2lHYXZVdjVGZXNtR3Bub2ZTdldYQlJPRzN5MXBUUFVDcGNIMFY5K3BMS2JXUGNZeHlMODdMSFU5S0xsdGJsZFpwRDNmSFNjZmxOSUpmVDkvdnRWc1ZieE5qWncrQlVFaENXTDFkazA5by9tdWsxMGIxUXY5ZVNnNE9iRTZnbjNqUHFOL3h2eHlVWVd3d0dGMUpjUHNzZWM0K1g4SElyZFRpSmhGcys0Yzd5Y3YzZmNodG9paHBVVzAxWmFLMU9MemNWZk5aRzVQbk1sSHZ4akJSVjgza2s5dUxZWktyQWN1bWM1WGRwWWMiLCJtYWMiOiI2MmFhZTI5ODg1NzM3MzJiNmRmNjUwMjA2MDI4NDliZWE5NWJmMzE0NDA4OGYwNjE3YzkzNWY5M2QwZWUxODk3IiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 03:35:28 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjExR2o5NFpSRXhTZ2E1S0I2OUhrTGc9PSIsInZhbHVlIjoiaSs2ZitWUitPcFk1NUVQSWZWdUZQazBUMHdMandxRkExMmNwRGxrUjFYVFloOFpiZktGU2EzNHJGVXh5d1NJSTZQVGF3SDlQTnNuWTdxeEs2SDJrcDZLVWdoeXVBSitqK3Z0b085V2lMSjVlTlZ4TUluVHJJbkx0L0VidTZSSkhYYi9iMk40bGlGUXdnV2xSQzRCTFFCWllKM3BpelNSYzd3STFKZ2M2Njk0WmgwNjRCblQwcXFacUFteU5GQi9qclUva2ZNZEJYTHozSGhPcEZ2dnhsSG43MGRpdzA2MFN3d0RuSEhja1NFSXE3b1dZRXdVK01TZHRpWHRLQUtYNUxtZEZwc1BGa3laUEtCUjNCdEZOd24yaElBdm96elpTa243RjJFODBYUW9rZzltZGVVNC9uTUFLcVpEREtkemxkdFBEVERmU1h2QVVueENYL29SSEJVZXdzbWRmL3dSTHFQN0RCYzNqc1RyVkZOT1FaNUNEekd2OWN4aEhqT2xXZE1EWHFXclFZd0ZyY0tCMTY5TzllZWRvcVUvY2pIbHBPQzdqdDdrVHhzVDJVaFhIQ2dkN2FSUDkwTU15NDJraXNqVi9KeXR0cEdBN1VtaXVGMUxBSUwzSkRNU09iUGVHa09OMHREMDZQRTI2ZjNOYUFQSGhuR1VXZFNCWXVDSUgiLCJtYWMiOiI5YmRjN2Y1MjQ4YjQzMmNmNjM1MGNhMGJhOWYzZjViODJhNmZkZjgyODQ2MDIwNDQ0ZjMyNmVlMmZiZWI4YTcxIiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 03:35:28 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjVvS3VndFZZa0F2dmtYa0ZDRVJQU3c9PSIsInZhbHVlIjoieVp1aERXNW5BL1UyUnlLTUZhOXNocHJUd0o2NFBDKy8rckk5SkZNYjk5cXM3clBVNjNLN2FTQ1dPc3pQOU5QcVUxMDFxRmNYWi85RWZOa3JTdnphR20rNlcvd2YvSjRGdytjNmRPQzg3aTcxNjg5SEd4bUgxTWFsODNEamppZ3B6S1gvaGRZTW9PcTBBaUQ4TGw3U2pNOWg4b0pJUk9jWjNHVXB5UGdqemQ3LzJZc0Y2cWhLOWdPYWY5czJpK3VnM2tXRDhQN3JQc2FIV0FrNnBQWFAvWkJFRnRmN3kyZ200cEVWR2pPZHZ5ZHhCMktVY1lVY2Z4cVJVSTNmUHQ2K0R5NHNidkMwRlJCU0U2ayttdlpNeXpyQ2lHYXZVdjVGZXNtR3Bub2ZTdldYQlJPRzN5MXBUUFVDcGNIMFY5K3BMS2JXUGNZeHlMODdMSFU5S0xsdGJsZFpwRDNmSFNjZmxOSUpmVDkvdnRWc1ZieE5qWncrQlVFaENXTDFkazA5by9tdWsxMGIxUXY5ZVNnNE9iRTZnbjNqUHFOL3h2eHlVWVd3d0dGMUpjUHNzZWM0K1g4SElyZFRpSmhGcys0Yzd5Y3YzZmNodG9paHBVVzAxWmFLMU9MemNWZk5aRzVQbk1sSHZ4akJSVjgza2s5dUxZWktyQWN1bWM1WGRwWWMiLCJtYWMiOiI2MmFhZTI5ODg1NzM3MzJiNmRmNjUwMjA2MDI4NDliZWE5NWJmMzE0NDA4OGYwNjE3YzkzNWY5M2QwZWUxODk3IiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 03:35:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjExR2o5NFpSRXhTZ2E1S0I2OUhrTGc9PSIsInZhbHVlIjoiaSs2ZitWUitPcFk1NUVQSWZWdUZQazBUMHdMandxRkExMmNwRGxrUjFYVFloOFpiZktGU2EzNHJGVXh5d1NJSTZQVGF3SDlQTnNuWTdxeEs2SDJrcDZLVWdoeXVBSitqK3Z0b085V2lMSjVlTlZ4TUluVHJJbkx0L0VidTZSSkhYYi9iMk40bGlGUXdnV2xSQzRCTFFCWllKM3BpelNSYzd3STFKZ2M2Njk0WmgwNjRCblQwcXFacUFteU5GQi9qclUva2ZNZEJYTHozSGhPcEZ2dnhsSG43MGRpdzA2MFN3d0RuSEhja1NFSXE3b1dZRXdVK01TZHRpWHRLQUtYNUxtZEZwc1BGa3laUEtCUjNCdEZOd24yaElBdm96elpTa243RjJFODBYUW9rZzltZGVVNC9uTUFLcVpEREtkemxkdFBEVERmU1h2QVVueENYL29SSEJVZXdzbWRmL3dSTHFQN0RCYzNqc1RyVkZOT1FaNUNEekd2OWN4aEhqT2xXZE1EWHFXclFZd0ZyY0tCMTY5TzllZWRvcVUvY2pIbHBPQzdqdDdrVHhzVDJVaFhIQ2dkN2FSUDkwTU15NDJraXNqVi9KeXR0cEdBN1VtaXVGMUxBSUwzSkRNU09iUGVHa09OMHREMDZQRTI2ZjNOYUFQSGhuR1VXZFNCWXVDSUgiLCJtYWMiOiI5YmRjN2Y1MjQ4YjQzMmNmNjM1MGNhMGJhOWYzZjViODJhNmZkZjgyODQ2MDIwNDQ0ZjMyNmVlMmZiZWI4YTcxIiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 03:35:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OxCA9rABwnsUtdJrsRyx2DLpR7CHtGXPC1CLyExX</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/forms</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}