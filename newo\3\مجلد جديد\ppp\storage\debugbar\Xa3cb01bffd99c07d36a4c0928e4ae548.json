{"__meta": {"id": "Xa3cb01bffd99c07d36a4c0928e4ae548", "datetime": "2025-06-21 01:36:14", "utime": **********.047742, "method": "GET", "uri": "/users/15/login-with-company", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750469772.782635, "end": **********.047776, "duration": 1.2651410102844238, "duration_str": "1.27s", "measures": [{"label": "Booting", "start": 1750469772.782635, "relative_start": 0, "end": **********.912326, "relative_end": **********.912326, "duration": 1.1296911239624023, "duration_str": "1.13s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.912348, "relative_start": 1.1297130584716797, "end": **********.04778, "relative_end": 4.0531158447265625e-06, "duration": 0.13543200492858887, "duration_str": "135ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44846960, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET users/{id}/login-with-company", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\UserController@LoginWithCompany", "namespace": null, "prefix": "", "where": [], "as": "login.with.company", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FUserController.php&line=660\" onclick=\"\">app/Http/Controllers/UserController.php:660-667</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01531, "accumulated_duration_str": "15.31ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.987878, "duration": 0.01418, "duration_str": "14.18ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 92.619}, {"sql": "select * from `users` where `users`.`id` = '15' limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\UserController.php", "line": 662}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0112488, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "UserController.php:662", "source": "app/Http/Controllers/UserController.php:662", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FUserController.php&line=662", "ajax": false, "filename": "UserController.php", "line": "662"}, "connection": "ty", "start_percent": 92.619, "width_percent": 7.381}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "OxCA9rABwnsUtdJrsRyx2DLpR7CHtGXPC1CLyExX", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users/15/login-with-company\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/users/15/login-with-company", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1107538029 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1107538029\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2003821774 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2003821774\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1448584831 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1994 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwy%7C0%7C1960; _clsk=1updagk%7C1750469741656%7C8%7C1%7Cj.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImxIYnF1aXBFL0luejdZbHkwcEd6ZkE9PSIsInZhbHVlIjoiT2tLbjZYRWxHdVlBZmdyR3dYSk1acWtSZ3YzRVV4RTJVc1JSQU04VzZ5dzZCakR2dEtLL29UNW5KUUdtcktjWTl6L0l3SVVMT1FZMTF5US9EQThHRnVzRUczdVYrZVV5all0UDNnbVRhR251RDFRbXduMFFVNERpRDV1Yk13ZStWeU1lSUFrNzh3UHdOVC9rbk82aktDYytSNGtCNWFCVVMrTWR2MGJzTnRhSXNvNXVyVWdVbjVrcFlQVVhzUXk0WHJSQTdCV1VBeU5aRk1DWUhOTU12ZVExQnE1Q2x4cnlNQTNvS2I5dGdwNENRNjdjQlNNTWJjelBkWm1WNnFzbjlPZmlVZUhpbW9WWVVDaUJudm84T240OHhIZ2hSQW1GVmxKd3JxbGxnMlFqeUl0TjRydlB3WjVDcVNpaXM5a3JzcW1scDhKdllpeHBWTHNKOE1tdWd4UFNYL2ZSYWcvcW8veWU1WEJYSWhJMDRVUm1wbFRybUFzRlVOV0tVZnFsUHJTclgwZis1cks2VDhjYXpkdUdsMUNXeGZQWTVydVRXMG1oTm1kU08zUGw1ZmN2eG1HbUo2MkN3Vy9sZUpscERmT0ZiZzJIazZpK3hpU2s4YlNUN0JvM0UwZXZDczd5SEI4YXBPSXRqaTJuYUVzL1JCZTNORmF6M3pBYUY2U1YiLCJtYWMiOiI3OWVlN2Y4NzUyYTczZmFhODJhYmZjMzExZTYxZjE0YzM2M2Q3NGIwYTAwNTAwMDU4ODg3ZjZkYjA1ZWE5MmM0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImhFeTVMU0UvWUJwQXBudkRoOFlnN0E9PSIsInZhbHVlIjoibVZXNjBJcjZ5cU5nVkFYREkrTFpVRm56T0o0aFp5T29vTlJaZ2lhZExSUDJmaFZtbU9ETXpwOGFWQzdBeC8zOVBlNFdHRVVEcURXQnB6YUhCRjlyTmFPdmltV05xWmNxNVNZdWNoUjhGNHFCRDJ0ckVEVXJQcS9RaFgzSWdrcjJLeC9YUVV5QWlkaDNFWXp0RUE3QzBLLy9yVjk4ZGY0NVh6KzFNeVBYQk8zV21ycWMvUUZCUmRwRndDREV2bzdWdkxhN0l1YTYwbzMrS2RTcCtvS1dOeG9MTjU5bFNoR3pyVFBtNFdKdEdMdzBOeFZsOVZlNjFKZzNPZC83ZnQ5aVM5cW5PblZIMGo1cTVycG5RaXkra2N4RXkvay9oUWFIeW1FRGNrUXd0RXl3aWs3aHdvUTVkM0lXN1ZDY1VJWFRWamVkWVVGN0xwd3FLQ1JReTQ2QkQycjdZYUJveHBOYmRMNTRhZThDTXNBTW5DTG81ZVNiV2JlUTVNNlU3a2ovbE12UG9KRlRSdDVYanYzVGhlU0NQblBlV042L1JKVjNDOTYyZ0ZBVVkydFIwVFJSSm9ETVlDb2NIQmdrWGNQaTVZeDlZc1ZhYUpBSldLWTRmWmpCL0hZMFVZTjUvTlQyTE5rRlZrNkRDVHAvd0tCNHdzbHNySnkvUmpRaFlZUEUiLCJtYWMiOiJkYzM5ZjMzZGU1OGNjZDk4YWZiMzVjMTVkNmM2MDc0NWVkOWE5MmY5N2M5YTFkYTI1M2VkZDM4MGI1NDUzYmE5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1448584831\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-941804285 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OxCA9rABwnsUtdJrsRyx2DLpR7CHtGXPC1CLyExX</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">h4v7KnclY0iOKyPMFu5GxMLqVTN5plmJ6SYNK0tY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-941804285\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2003864630 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 21 Jun 2025 01:36:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlpjWVk3Sm9oQjkzWmpOS0xoLytCM1E9PSIsInZhbHVlIjoiNGwxMDZLQ0F4emR1NGlkOXAyY2E4WG9lRlJiaW5tOHdLeXpFM0NyaERTZ1BNOVBVbk5iNGFnM1ZraVhiSk5TQllGK0NBL1NMWnR2V0p2YUtMTjFLNjlZS3QzQUhZbzJDenBXV3hZNjl3MStvT0ZmSjNjd0wwWUR2WmplSEhVc0FCSDA5Q0lZcHVqRDdqUytiNzNWdjZrSHhLZHo2UnQ4Z0JWeXRiR00zUjl6UDhEd2RFUTZSNUFVOHZYbCt6SkFveW1kSXVuaG1oK2MrODQyTkFVUExsODhhNnJ1SGZ6bUVYSGJkN0lPMGVwRkthaUVqZndrN3lBWi9KQkVDR0kzcFE0eWF2QjdRWndaT2VGanQ0ZG1nNEZiN3pOUDdOMVVoUzNzVXkwZUljQzVteWVmMEx1dDlpSkVNQXZNbmtMZVNZUXAzNDZqSTd4NGorVGVIMHdvSHNqbllSemN2VGtJM3VzTnpGRWxmRVo0a3djaUhZQlVlV3ZjRGRIY0NOM1FLTUNIQm1URkVtek14MThhSUhtaGVvV2U0SDFKQll2REhiMkRnbGxJZlZTcFltNmtsUHQ4WWxqaXNlZDlWNGx6NmFPaWtzL0lWMi9adlc0NmFadmFkNzBhRFpoVG5DS2Evb1BEQUx1eEFrbTg5cytOenFzNjBNZXdXM0RvNjc4dkIiLCJtYWMiOiI5OTU3ODkyYzM2NTQxM2Q1MGUwOTE3NzVkOTJhMzNhZDg1YWRlMTM4MzhkN2JkZjdiM2E2ODQ4MDAyYjliYjA1IiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 03:36:14 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkFhaUd1Szczd2dhWi9BenVjRU5xVUE9PSIsInZhbHVlIjoibkk2U05LVllmaFBPUVF6djd1TnNnQjd2K3lBcnJpcEwrQm9QNXhCaDcrUlBXaVJ1ZzA2Vk5OaWd1dVdtcVZ0b3h4eDgvNWxpWkhhQUNrZUwwK3JraG1jcy9tRXl2MXRxdHFvWktjRVl3bGFNYy95cXd4bENaNE1wYmZJdngwUE1YelE5cENobS9Mc25ybDJnOUVSRGt2Nkt0K0c1N21NSHQ2Wm9leHREU2pqVFpSU25hTWx5aGNyWHdNOWlTdTVodmVJd2RSVlA4SWcrMXZrTUFCMkE0bE9jcEsvZzRXT0dkbVlYV2ttYy9kdUNpUDBrQlo1RUNqcWI0ODNxZGg0dmJGRmk4MG9zaTdOVFhsWHhIQ0h5ZWZ0d1VUVytvUDVQRTBvN2xqSElDdHFNaXIwQ0MzNjdoY0pyT29TOC9WaTVxdkZ3OVA3Q0hLbjJydG1pYVp0dUdHdjNuQmQ1aEFueURxQVpxdEdSQjhVTUVHUVhBZ3p1RXQxWk5mNHJpNDZDcTRacmJFVUh5ZU5KNWwzajUvY3lkZE1iZ3V2a3hJdWVDSll6L28rcTRaOW1lakU4b1B6MG90N01QYWdxREN1WWlyUy94NmhQaEFKVGxVQVJ5S05Kc2Q0ZTlDTERKb2ZHUGFJdjVPNHgwbXQ3VGczN1FyUThjdTJvN0JZUkt4OUsiLCJtYWMiOiI2ZDA2ZGJiYjc0NjIxM2RmZDc3MTJmYTZmOTI4OWIzM2ExNTE3NmU3YzY5ZDk1MDY0ZTEwMGUyZGZlMzhkNWY2IiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 03:36:14 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlpjWVk3Sm9oQjkzWmpOS0xoLytCM1E9PSIsInZhbHVlIjoiNGwxMDZLQ0F4emR1NGlkOXAyY2E4WG9lRlJiaW5tOHdLeXpFM0NyaERTZ1BNOVBVbk5iNGFnM1ZraVhiSk5TQllGK0NBL1NMWnR2V0p2YUtMTjFLNjlZS3QzQUhZbzJDenBXV3hZNjl3MStvT0ZmSjNjd0wwWUR2WmplSEhVc0FCSDA5Q0lZcHVqRDdqUytiNzNWdjZrSHhLZHo2UnQ4Z0JWeXRiR00zUjl6UDhEd2RFUTZSNUFVOHZYbCt6SkFveW1kSXVuaG1oK2MrODQyTkFVUExsODhhNnJ1SGZ6bUVYSGJkN0lPMGVwRkthaUVqZndrN3lBWi9KQkVDR0kzcFE0eWF2QjdRWndaT2VGanQ0ZG1nNEZiN3pOUDdOMVVoUzNzVXkwZUljQzVteWVmMEx1dDlpSkVNQXZNbmtMZVNZUXAzNDZqSTd4NGorVGVIMHdvSHNqbllSemN2VGtJM3VzTnpGRWxmRVo0a3djaUhZQlVlV3ZjRGRIY0NOM1FLTUNIQm1URkVtek14MThhSUhtaGVvV2U0SDFKQll2REhiMkRnbGxJZlZTcFltNmtsUHQ4WWxqaXNlZDlWNGx6NmFPaWtzL0lWMi9adlc0NmFadmFkNzBhRFpoVG5DS2Evb1BEQUx1eEFrbTg5cytOenFzNjBNZXdXM0RvNjc4dkIiLCJtYWMiOiI5OTU3ODkyYzM2NTQxM2Q1MGUwOTE3NzVkOTJhMzNhZDg1YWRlMTM4MzhkN2JkZjdiM2E2ODQ4MDAyYjliYjA1IiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 03:36:14 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkFhaUd1Szczd2dhWi9BenVjRU5xVUE9PSIsInZhbHVlIjoibkk2U05LVllmaFBPUVF6djd1TnNnQjd2K3lBcnJpcEwrQm9QNXhCaDcrUlBXaVJ1ZzA2Vk5OaWd1dVdtcVZ0b3h4eDgvNWxpWkhhQUNrZUwwK3JraG1jcy9tRXl2MXRxdHFvWktjRVl3bGFNYy95cXd4bENaNE1wYmZJdngwUE1YelE5cENobS9Mc25ybDJnOUVSRGt2Nkt0K0c1N21NSHQ2Wm9leHREU2pqVFpSU25hTWx5aGNyWHdNOWlTdTVodmVJd2RSVlA4SWcrMXZrTUFCMkE0bE9jcEsvZzRXT0dkbVlYV2ttYy9kdUNpUDBrQlo1RUNqcWI0ODNxZGg0dmJGRmk4MG9zaTdOVFhsWHhIQ0h5ZWZ0d1VUVytvUDVQRTBvN2xqSElDdHFNaXIwQ0MzNjdoY0pyT29TOC9WaTVxdkZ3OVA3Q0hLbjJydG1pYVp0dUdHdjNuQmQ1aEFueURxQVpxdEdSQjhVTUVHUVhBZ3p1RXQxWk5mNHJpNDZDcTRacmJFVUh5ZU5KNWwzajUvY3lkZE1iZ3V2a3hJdWVDSll6L28rcTRaOW1lakU4b1B6MG90N01QYWdxREN1WWlyUy94NmhQaEFKVGxVQVJ5S05Kc2Q0ZTlDTERKb2ZHUGFJdjVPNHgwbXQ3VGczN1FyUThjdTJvN0JZUkt4OUsiLCJtYWMiOiI2ZDA2ZGJiYjc0NjIxM2RmZDc3MTJmYTZmOTI4OWIzM2ExNTE3NmU3YzY5ZDk1MDY0ZTEwMGUyZGZlMzhkNWY2IiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 03:36:14 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2003864630\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OxCA9rABwnsUtdJrsRyx2DLpR7CHtGXPC1CLyExX</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://localhost/users/15/login-with-company</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}