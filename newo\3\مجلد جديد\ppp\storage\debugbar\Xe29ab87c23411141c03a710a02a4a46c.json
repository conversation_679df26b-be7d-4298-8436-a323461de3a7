{"__meta": {"id": "Xe29ab87c23411141c03a710a02a4a46c", "datetime": "2025-06-21 01:33:45", "utime": **********.305691, "method": "GET", "uri": "/", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750469624.062514, "end": **********.305722, "duration": 1.2432079315185547, "duration_str": "1.24s", "measures": [{"label": "Booting", "start": 1750469624.062514, "relative_start": 0, "end": **********.167551, "relative_end": **********.167551, "duration": 1.105036973953247, "duration_str": "1.11s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.167579, "relative_start": 1.105064868927002, "end": **********.305725, "relative_end": 3.0994415283203125e-06, "duration": 0.13814616203308105, "duration_str": "138ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44453440, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@landingpage", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=59\" onclick=\"\">app/Http/Controllers/DashboardController.php:59-74</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.005900000000000001, "accumulated_duration_str": "5.9ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\DashboardController.php", "line": 66}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.268835, "duration": 0.005900000000000001, "duration_str": "5.9ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "ty", "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LHbTVxCjEz1Eo0lHmOrwmF9rHtoQFcRzzqOa1VuX", "_previous": "array:1 [\n  \"url\" => \"http://localhost\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1357701863 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1357701863\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-691672557 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-691672557\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-698813124 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-698813124\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-153676728 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-153676728\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-362494343 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 21 Jun 2025 01:33:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImxBSWJjMEhRd2F4ODRjdFZSTDFTQVE9PSIsInZhbHVlIjoiUkp5QWZUdit4UVg1aFZNaUx4TFU5UE1COUZCRUwzdkZsOTFaL0MwTWdzUy83TEkzMklONDJWNmhLMC93MkdoZUV6T1VpTVB3ZURuR3RZdXRETE1vYy9XaTNrR00yKzdMMnNJRlBUQ3dhY1FZNjJ3RndoTWgzQXI1UVUveHUvUktVbjMzQ1FPOHduR2dOazR1WEdib1B2UkgyS1JFMWxsRXlQMGk5K213TkJZek1wZkUxSXNhNHZ6Mm9nNVBzUGdXZ1Y0ZG1uTXovWkcwK0RkN2xGZWxHZUtxZ0VacWN0aDVjaVBFenRtc1lNNnR6N0UzMHlUeEljNFlpNTg4Q1I4ZnRINEFJM1Yrb0tzRWpybW5ybmREdExLejhRNHZITkk0T0FxVmpGTDRqK2pwQ3AxaGNMS2tJYWo0ck5RNExEcmlCZkQyM2ZBQkJBUWRZekRoUXlBOTFqekhVR2VTbis2dHdzVVJoa1g2cVE2UWlDZ1FWL1lNY3Y0U2lETktScVNFT01MTjB3c3djeVhmWHdKY0ZQc2ZMYzNnTkRXalFuWk9DQW9JcFFSY3NsY3hxTHkxTWx1MTEwZmhkNkhOOWxBc0swOXpKTldCUTNMUDd1ZW1MdXNwdlp3L25ldTNDckFGNVVVMFFqcStGN2IyQUVlajBOQW91Z3JhbU5wSFU0WXoiLCJtYWMiOiI3NjJmNzljZTEwZjJjYjEwM2Q5YzRiZDRjY2EzMDQzNzhiNzYzOTdiZDYzYmIxMTllMjc0MzRhMzU0NDgwNGQyIiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 03:33:45 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlZjSjFDL2VFZWUxQXJaMC90Mm9JOUE9PSIsInZhbHVlIjoiUyt2UXkra2Q5bmpqTEQwdmlkd3FIT0FVUzhUcloxM2tnaUt3OGY5d0d0OEFaeklYZmxzS0hYcFFGTit4ZTlxQTJuMDVoRDFIV2pMaHcxUXNmSXdsTk02SjRhcXhPYkt6aWxNbUMrVlRldlZDMnFHaGw3RFBGUGNMakFheGYwMHVZME9tOE1aR01jNlN6dGFCd0doK3BFZWdhZzYrKzkwZDdXT3Q5eGhrVURFdlUySVFoYWN1ZTRZTkwxRVVFaXhEME5JSWxTd2JlK1pCUzI3QU51dmQxZE5jZlZXZXY3Qmx3SGx3WENVeHRySkVkb3pYei9TSkltd3g4cWV3OThzWVRhdk1qaDdZY3VlUVRBejNaWitKeGljSk9SRHV0anFWRTdIMmdaOUllczh6cVFLN1NlSVVUZXpPOFdaYnNmU0xTeG1aMll6UU5pSzIzRTZ6dXE2RWhwMzh3WVQ0bFFpTDkyazhsQ01FZ29lVndYclhNNVlaSGRzQVpBNTd4dEt4L3hReS9UUk5XNWJQVzl6RVEzdFFtQ2JTekovZy9VdkxLNG5RdXlpNGg4STlXVDJYT0hORXVoRXh0YlBzQlVpSUM2YWRhU2pQRWc1RnUrZlU0OENpWm43bnN2KzlpMUFndnJjS2dhRGV1UjZwdEhKVEhJeHY2Q1ZqVTdkMVNZcmciLCJtYWMiOiJjMmNhN2E4NDMyNzg3MGQ0NWMzZWIzYjAzZWU4MTg4N2EwYjM1NTRiYzFlNzhiYjA1ODc2ZmYyZWRlZjg5ZGQ2IiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 03:33:45 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImxBSWJjMEhRd2F4ODRjdFZSTDFTQVE9PSIsInZhbHVlIjoiUkp5QWZUdit4UVg1aFZNaUx4TFU5UE1COUZCRUwzdkZsOTFaL0MwTWdzUy83TEkzMklONDJWNmhLMC93MkdoZUV6T1VpTVB3ZURuR3RZdXRETE1vYy9XaTNrR00yKzdMMnNJRlBUQ3dhY1FZNjJ3RndoTWgzQXI1UVUveHUvUktVbjMzQ1FPOHduR2dOazR1WEdib1B2UkgyS1JFMWxsRXlQMGk5K213TkJZek1wZkUxSXNhNHZ6Mm9nNVBzUGdXZ1Y0ZG1uTXovWkcwK0RkN2xGZWxHZUtxZ0VacWN0aDVjaVBFenRtc1lNNnR6N0UzMHlUeEljNFlpNTg4Q1I4ZnRINEFJM1Yrb0tzRWpybW5ybmREdExLejhRNHZITkk0T0FxVmpGTDRqK2pwQ3AxaGNMS2tJYWo0ck5RNExEcmlCZkQyM2ZBQkJBUWRZekRoUXlBOTFqekhVR2VTbis2dHdzVVJoa1g2cVE2UWlDZ1FWL1lNY3Y0U2lETktScVNFT01MTjB3c3djeVhmWHdKY0ZQc2ZMYzNnTkRXalFuWk9DQW9JcFFSY3NsY3hxTHkxTWx1MTEwZmhkNkhOOWxBc0swOXpKTldCUTNMUDd1ZW1MdXNwdlp3L25ldTNDckFGNVVVMFFqcStGN2IyQUVlajBOQW91Z3JhbU5wSFU0WXoiLCJtYWMiOiI3NjJmNzljZTEwZjJjYjEwM2Q5YzRiZDRjY2EzMDQzNzhiNzYzOTdiZDYzYmIxMTllMjc0MzRhMzU0NDgwNGQyIiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 03:33:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlZjSjFDL2VFZWUxQXJaMC90Mm9JOUE9PSIsInZhbHVlIjoiUyt2UXkra2Q5bmpqTEQwdmlkd3FIT0FVUzhUcloxM2tnaUt3OGY5d0d0OEFaeklYZmxzS0hYcFFGTit4ZTlxQTJuMDVoRDFIV2pMaHcxUXNmSXdsTk02SjRhcXhPYkt6aWxNbUMrVlRldlZDMnFHaGw3RFBGUGNMakFheGYwMHVZME9tOE1aR01jNlN6dGFCd0doK3BFZWdhZzYrKzkwZDdXT3Q5eGhrVURFdlUySVFoYWN1ZTRZTkwxRVVFaXhEME5JSWxTd2JlK1pCUzI3QU51dmQxZE5jZlZXZXY3Qmx3SGx3WENVeHRySkVkb3pYei9TSkltd3g4cWV3OThzWVRhdk1qaDdZY3VlUVRBejNaWitKeGljSk9SRHV0anFWRTdIMmdaOUllczh6cVFLN1NlSVVUZXpPOFdaYnNmU0xTeG1aMll6UU5pSzIzRTZ6dXE2RWhwMzh3WVQ0bFFpTDkyazhsQ01FZ29lVndYclhNNVlaSGRzQVpBNTd4dEt4L3hReS9UUk5XNWJQVzl6RVEzdFFtQ2JTekovZy9VdkxLNG5RdXlpNGg4STlXVDJYT0hORXVoRXh0YlBzQlVpSUM2YWRhU2pQRWc1RnUrZlU0OENpWm43bnN2KzlpMUFndnJjS2dhRGV1UjZwdEhKVEhJeHY2Q1ZqVTdkMVNZcmciLCJtYWMiOiJjMmNhN2E4NDMyNzg3MGQ0NWMzZWIzYjAzZWU4MTg4N2EwYjM1NTRiYzFlNzhiYjA1ODc2ZmYyZWRlZjg5ZGQ2IiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 03:33:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-362494343\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-292069529 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LHbTVxCjEz1Eo0lHmOrwmF9rHtoQFcRzzqOa1VuX</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-292069529\", {\"maxDepth\":0})</script>\n"}}