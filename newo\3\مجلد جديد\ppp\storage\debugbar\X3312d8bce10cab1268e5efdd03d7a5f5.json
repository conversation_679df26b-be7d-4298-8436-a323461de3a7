{"__meta": {"id": "X3312d8bce10cab1268e5efdd03d7a5f5", "datetime": "2025-06-21 01:34:22", "utime": **********.121742, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750469660.68026, "end": **********.121775, "duration": 1.4415149688720703, "duration_str": "1.44s", "measures": [{"label": "Booting", "start": 1750469660.68026, "relative_start": 0, "end": 1750469661.932858, "relative_end": 1750469661.932858, "duration": 1.2525980472564697, "duration_str": "1.25s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1750469661.932878, "relative_start": 1.2526180744171143, "end": **********.121779, "relative_end": 4.0531158447265625e-06, "duration": 0.18890094757080078, "duration_str": "189ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46090528, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02658, "accumulated_duration_str": "26.58ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.023504, "duration": 0.022109999999999998, "duration_str": "22.11ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 83.183}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.0727382, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 83.183, "width_percent": 5.794}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.0909271, "duration": 0.0029300000000000003, "duration_str": "2.93ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 88.977, "width_percent": 11.023}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "bSqGKRzn8zrN52whBJ5jpetFrYHJ06phOnPEUsYY", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-901241196 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-901241196\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1106194656 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1106194656\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1292475150 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bSqGKRzn8zrN52whBJ5jpetFrYHJ06phOnPEUsYY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1292475150\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1833 characters\">_clck=h9hzj7%7C2%7Cfwy%7C0%7C1998; _clsk=1pfi5p6%7C1750469631526%7C1%7C1%7Cj.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImNLb1F5bUwrcnFtSmo1REozUW9Rc1E9PSIsInZhbHVlIjoiR21laVBNektOSzVDSy8xM1M4bEdtaXEwUEczYUd2MHVTcXI0dTJaQXZVT2doYW9XbWxoYmJUc3dxQXJIZXRMekg1STdvcWxodjJHeU0zUWVhN2wyUGNHcTNFWXdpYzZVUVBYWHlKRjkrRHlvUyswUHFEL1FIaTQxZTdCeWJxUExIUytpTHJ2ZFpYK0ZwcjUycDVXTUNVS1ZpaGc2eC9LTmJ0ZTQ1VS9xTWp6alVUU1h3c1BQVDRqUFZMSk9oaE5CZkFEK0EyR2NGOEJXMTMzMTRzODF5UElvY0d5QXF4WHB6aTRiTmdGRUFpRWhUbEhYUVpPSGxDMDlod0VtRDBadTlqbXkzR1plUnF5K3JwaWxBMllLZ1A0UDQ4OFpXNzdOa2E5Y2FMZzFoKy9DQ0xaaTZyWWJDeVF0Y1daN3A0a3FTQVlpTzN6NS9ZT2FhaWs5NzVaeThpSk1MZVFENjBDMFRHMk9XUXJJOTdHS1RVREZpZHpoU1M3UzY5cnZENzd3Z3pyR2JvU25QZDgzNVUrQWgwTDUxcGp3Y3U4TDNmbnFiV1hIMmF3UFkyYithbldqWGpqbDhtZzdqZ0ZuaE80eTFQWE1YOXc0NWJScXlvRm5KeHE2VDkxVndpVGMydEJxZlVObHVOdWdzVWZGTG1sVmZ5MVpIaHhSKzlFVWVKVDYiLCJtYWMiOiJhZGZmNDkyN2UxOGU5NzkwNzUwMjMzYjAwNDQ5ZjhkMzBlMzRjODAwYjc2OTA2N2Y1OTU3Yjc4MDBiYjliNzlmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Iks5MWFWRi9yZlFvUEh3WXBYNzdnUGc9PSIsInZhbHVlIjoiOU0wVWN4M3llMVN0WVNRVThUWlVHSGpZN0ZoblNSZ3FvNFppRjU4TjhqNWhESjJRRFhyVDNqbzBBMGM3cVM2b2ZJSDNWZXZ1Ly96NDlSaVZ4V3ZmRFBKc01obWR6dm1GZWRseDkyYjRVWnpiODZRR2o0aERVOFZxQ2hVV0RleDFEcVUvWnp6c2xwVXB1OFhuVlJraURoeTFJQkRHOVdoV3draEpFOFdRRVNoRlQwMU0wMW5GM0hXbjYzTUpvbDl1L2lONUZMZUt3eUxXQlViRGEvMTRoQzlUcGpoZ1kwRkNQMEpSeHdHVXJLaVRRVmg4S1lEbG90VHF6SmRXR0c2VlpwTXQ4bVVaVjdLS3Fqd09QczU2TDc0QncyTTkvL2R6VVBqY2lEN1k2cGVIQlhxQnZ3OU5BendqZE9RWUttUmhxdkFQSEpFd2FyNHhIdlUrVGlxRmpWYUJwRXNGN0FxcFl0eDdjY2JqRThmNUsxZ1VFLzJud0xvQzRXcjlCRkJ6OExhaTIvUU15YW9iZy96eG9adGJZNnNjbi9RVUdRbGd0ZU9zcm9SdXlnNVhCa0w2Mk1GUEo0UU4rbkxTUE8yQ1dLUGhYTWdVdWpiLzg2RmNrb21WaldGYWUwOERjNWNvK095WFhwRDAyTUxpZHRnT2dyZnRySExCWWFpc2pmSGQiLCJtYWMiOiI0YWI1YmNhZDYwNTA2OGEyMDI3ZGVlYmFlZmZjOTA0NjY5YmIwZjk3ODMyN2NkMjM3OWI5ODM4NTBkN2I5ZmUxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bSqGKRzn8zrN52whBJ5jpetFrYHJ06phOnPEUsYY</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HB3SpfsNW9qh15emZQ8PQBnZsYUESoWIqimBy8Yg</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-834533791 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 21 Jun 2025 01:34:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IndGOVBGM0JLUTNxV2lsV3RzYmtDUnc9PSIsInZhbHVlIjoib1JBK2IxeXZ6VmpRTno3RVYxWlNPTldITnhRWkJpeXRkcnY2dldsZlpTdHRwdE5mWlRYbXpXVkd4bnI0THNzQlBKNXBoTVR0eTFiOUJla3BITmx5V1JQK1R5S3FFSXppS2srQUhWcGhiWCtKdERIOEZtNE1VeGpBUXdmM2xZUCtGWlF1ZnRxazdad05YY3Z5WUJYaDQvQm9ja2xybjdHQ1FuZWlCdXdnYytCNzdFVlBZc1dEUGJhckp3dkVrVDl2Q3pKNXZXM092OEFpcG9heWNsSzFnc3dkeHBPUUVnT2cycklBY0k5Z1oyNHpBMnJ4cUhnV0xXL2FYZGsrb0ZweGhsSWdSSHV0T3JWenZoVVAwOW10dWI2Ym84bWQwTVBKb0R1TDMweEllV21SY1NCbHhLZ0FNK2REVXR2WmRjYVo0aGpEVmc5QmhLZXo0TmdlMmxDUDJWUW1jSTN2RHlLc2NWYngvek93Smc3a3JtdkRhbUZzNmJhVDBDZkNnbENVYTd4OEFUcTBFWE5XcVhEbFJPMXF0THhSOGk1ZnZvWkNYTnBRNWNSc01IN1drL2JNQmNKSTZjZHVxU1NMV2JxTW1wcEtjTk9aMlRvZGJFaFVLbDlzUXhnWHFDRHltUTZVNlY5c2NyNjZjTkdlL2F5L1YrT040ZEdGaElDcm9VT2giLCJtYWMiOiIyMmFjMDcwZWFjYmQwOGE4MTBiYTQ3MGI3ZDUxZDM4MGYwZmM1NGRmODg5MWMzZTEyMGQ3YWJlY2ZlN2U0NTRlIiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 03:34:22 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkR5LzdNNWJFN0NsZDJDK0JZRFFuUUE9PSIsInZhbHVlIjoidi9aRjVyeVlUWlJRY2dVQ2orK2NKQVMzRWZVZ2NsUXdhOThJd1NDZjByNG9XQ2tQUzJ1c2NGMTBBZit3RHd5Z3RlSEJadGFKaC9GcWlLdXBGNTVUS2psOEd5TWt2UU9xQ3F0dWtVUmJwTWdEWm8vOXFnc0xDNDlnVWNmL3Y0QnZSL21pdXlCeEhmQmloVXB4ZGxGZy9sbmtsQ056UFVPZS9aMDF3TW1CWlpkS1ZYUW5LM1RWZFRBUXZIbWxrVHcwZ25JNU1wUWloR05uNlNmejhoaGlqYTRMZjJUUDdGTk9iVW5zMlMwTFdSTW91R1JhTnp6dXM1M0hvWnl5R0Y3T0pFemcxZldpSFlRaUFRTU5yclNOZjBaWUFKdllFZzB6c2VBbHhYK2loVlFCVnZjaUFYNmU5VE1wWjh2RHZzQ3RwbW9jRzN2SmdZcE94YnhjSW0vOWRSekdPN3ZOYXkvSUZVdzZTU2J6QWl0Wjh1QVgvakp4MFgxWHQ2NWlQZVE3M3FyeU91U1dSUDJEbVBTM1ltdnpjdER4cUxtWmVhL0M5RGZhVVhZTlBzSHdhQUJDckNQQkUvbFVwcU1vdmRmZHZVRkxncUNZRzdpYlJSSEVhWk1zTG40eFI4U0VabklreG9pV3I4LzNOOVNlSWJVRlo3RzhUdHlqR1pyaW1xcjQiLCJtYWMiOiIyODgzNjcwYzk5YmYxOTBhZDFmMDdmZGVlYThlYThhOWU0NDRjOTkyOGY4Nzc4YmY2NjUxMTI5YWM2MmNiMGM0IiwidGFnIjoiIn0%3D; expires=Sat, 21 Jun 2025 03:34:22 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IndGOVBGM0JLUTNxV2lsV3RzYmtDUnc9PSIsInZhbHVlIjoib1JBK2IxeXZ6VmpRTno3RVYxWlNPTldITnhRWkJpeXRkcnY2dldsZlpTdHRwdE5mWlRYbXpXVkd4bnI0THNzQlBKNXBoTVR0eTFiOUJla3BITmx5V1JQK1R5S3FFSXppS2srQUhWcGhiWCtKdERIOEZtNE1VeGpBUXdmM2xZUCtGWlF1ZnRxazdad05YY3Z5WUJYaDQvQm9ja2xybjdHQ1FuZWlCdXdnYytCNzdFVlBZc1dEUGJhckp3dkVrVDl2Q3pKNXZXM092OEFpcG9heWNsSzFnc3dkeHBPUUVnT2cycklBY0k5Z1oyNHpBMnJ4cUhnV0xXL2FYZGsrb0ZweGhsSWdSSHV0T3JWenZoVVAwOW10dWI2Ym84bWQwTVBKb0R1TDMweEllV21SY1NCbHhLZ0FNK2REVXR2WmRjYVo0aGpEVmc5QmhLZXo0TmdlMmxDUDJWUW1jSTN2RHlLc2NWYngvek93Smc3a3JtdkRhbUZzNmJhVDBDZkNnbENVYTd4OEFUcTBFWE5XcVhEbFJPMXF0THhSOGk1ZnZvWkNYTnBRNWNSc01IN1drL2JNQmNKSTZjZHVxU1NMV2JxTW1wcEtjTk9aMlRvZGJFaFVLbDlzUXhnWHFDRHltUTZVNlY5c2NyNjZjTkdlL2F5L1YrT040ZEdGaElDcm9VT2giLCJtYWMiOiIyMmFjMDcwZWFjYmQwOGE4MTBiYTQ3MGI3ZDUxZDM4MGYwZmM1NGRmODg5MWMzZTEyMGQ3YWJlY2ZlN2U0NTRlIiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 03:34:22 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkR5LzdNNWJFN0NsZDJDK0JZRFFuUUE9PSIsInZhbHVlIjoidi9aRjVyeVlUWlJRY2dVQ2orK2NKQVMzRWZVZ2NsUXdhOThJd1NDZjByNG9XQ2tQUzJ1c2NGMTBBZit3RHd5Z3RlSEJadGFKaC9GcWlLdXBGNTVUS2psOEd5TWt2UU9xQ3F0dWtVUmJwTWdEWm8vOXFnc0xDNDlnVWNmL3Y0QnZSL21pdXlCeEhmQmloVXB4ZGxGZy9sbmtsQ056UFVPZS9aMDF3TW1CWlpkS1ZYUW5LM1RWZFRBUXZIbWxrVHcwZ25JNU1wUWloR05uNlNmejhoaGlqYTRMZjJUUDdGTk9iVW5zMlMwTFdSTW91R1JhTnp6dXM1M0hvWnl5R0Y3T0pFemcxZldpSFlRaUFRTU5yclNOZjBaWUFKdllFZzB6c2VBbHhYK2loVlFCVnZjaUFYNmU5VE1wWjh2RHZzQ3RwbW9jRzN2SmdZcE94YnhjSW0vOWRSekdPN3ZOYXkvSUZVdzZTU2J6QWl0Wjh1QVgvakp4MFgxWHQ2NWlQZVE3M3FyeU91U1dSUDJEbVBTM1ltdnpjdER4cUxtWmVhL0M5RGZhVVhZTlBzSHdhQUJDckNQQkUvbFVwcU1vdmRmZHZVRkxncUNZRzdpYlJSSEVhWk1zTG40eFI4U0VabklreG9pV3I4LzNOOVNlSWJVRlo3RzhUdHlqR1pyaW1xcjQiLCJtYWMiOiIyODgzNjcwYzk5YmYxOTBhZDFmMDdmZGVlYThlYThhOWU0NDRjOTkyOGY4Nzc4YmY2NjUxMTI5YWM2MmNiMGM0IiwidGFnIjoiIn0%3D; expires=Sat, 21-Jun-2025 03:34:22 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-834533791\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1952785742 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bSqGKRzn8zrN52whBJ5jpetFrYHJ06phOnPEUsYY</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1952785742\", {\"maxDepth\":0})</script>\n"}}